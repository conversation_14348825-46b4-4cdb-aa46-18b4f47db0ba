/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define GPIO_HFXT_PORT                                                     GPIOA
#define GPIO_HFXIN_PIN                                             DL_GPIO_PIN_5
#define GPIO_HFXIN_IOMUX                                         (IOMUX_PINCM10)
#define GPIO_HFXOUT_PIN                                            DL_GPIO_PIN_6
#define GPIO_HFXOUT_IOMUX                                        (IOMUX_PINCM11)
#define CPUCLK_FREQ                                                     80000000



/* Defines for MotorAFront */
#define MotorAFront_INST                                                   TIMG7
#define MotorAFront_INST_IRQHandler                             TIMG7_IRQHandler
#define MotorAFront_INST_INT_IRQN                               (TIMG7_INT_IRQn)
#define MotorAFront_INST_CLK_FREQ                                        2000000
/* GPIO defines for channel 0 */
#define GPIO_MotorAFront_C0_PORT                                           GPIOB
#define GPIO_MotorAFront_C0_PIN                                   DL_GPIO_PIN_15
#define GPIO_MotorAFront_C0_IOMUX                                (IOMUX_PINCM32)
#define GPIO_MotorAFront_C0_IOMUX_FUNC               IOMUX_PINCM32_PF_TIMG7_CCP0
#define GPIO_MotorAFront_C0_IDX                              DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_MotorAFront_C1_PORT                                           GPIOB
#define GPIO_MotorAFront_C1_PIN                                   DL_GPIO_PIN_16
#define GPIO_MotorAFront_C1_IOMUX                                (IOMUX_PINCM33)
#define GPIO_MotorAFront_C1_IOMUX_FUNC               IOMUX_PINCM33_PF_TIMG7_CCP1
#define GPIO_MotorAFront_C1_IDX                              DL_TIMER_CC_1_INDEX

/* Defines for MotorBFront */
#define MotorBFront_INST                                                   TIMA1
#define MotorBFront_INST_IRQHandler                             TIMA1_IRQHandler
#define MotorBFront_INST_INT_IRQN                               (TIMA1_INT_IRQn)
#define MotorBFront_INST_CLK_FREQ                                       80000000
/* GPIO defines for channel 0 */
#define GPIO_MotorBFront_C0_PORT                                           GPIOB
#define GPIO_MotorBFront_C0_PIN                                    DL_GPIO_PIN_2
#define GPIO_MotorBFront_C0_IOMUX                                (IOMUX_PINCM15)
#define GPIO_MotorBFront_C0_IOMUX_FUNC               IOMUX_PINCM15_PF_TIMA1_CCP0
#define GPIO_MotorBFront_C0_IDX                              DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_MotorBFront_C1_PORT                                           GPIOB
#define GPIO_MotorBFront_C1_PIN                                    DL_GPIO_PIN_3
#define GPIO_MotorBFront_C1_IOMUX                                (IOMUX_PINCM16)
#define GPIO_MotorBFront_C1_IOMUX_FUNC               IOMUX_PINCM16_PF_TIMA1_CCP1
#define GPIO_MotorBFront_C1_IDX                              DL_TIMER_CC_1_INDEX




/* Defines for I2C_OLED */
#define I2C_OLED_INST                                                       I2C0
#define I2C_OLED_INST_IRQHandler                                 I2C0_IRQHandler
#define I2C_OLED_INST_INT_IRQN                                     I2C0_INT_IRQn
#define I2C_OLED_BUS_SPEED_HZ                                             400000
#define GPIO_I2C_OLED_SDA_PORT                                             GPIOA
#define GPIO_I2C_OLED_SDA_PIN                                      DL_GPIO_PIN_0
#define GPIO_I2C_OLED_IOMUX_SDA                                   (IOMUX_PINCM1)
#define GPIO_I2C_OLED_IOMUX_SDA_FUNC                    IOMUX_PINCM1_PF_I2C0_SDA
#define GPIO_I2C_OLED_SCL_PORT                                             GPIOA
#define GPIO_I2C_OLED_SCL_PIN                                      DL_GPIO_PIN_1
#define GPIO_I2C_OLED_IOMUX_SCL                                   (IOMUX_PINCM2)
#define GPIO_I2C_OLED_IOMUX_SCL_FUNC                    IOMUX_PINCM2_PF_I2C0_SCL


/* Defines for UART_K230 */
#define UART_K230_INST                                                     UART2
#define UART_K230_INST_FREQUENCY                                        40000000
#define UART_K230_INST_IRQHandler                               UART2_IRQHandler
#define UART_K230_INST_INT_IRQN                                   UART2_INT_IRQn
#define GPIO_UART_K230_RX_PORT                                             GPIOB
#define GPIO_UART_K230_TX_PORT                                             GPIOB
#define GPIO_UART_K230_RX_PIN                                     DL_GPIO_PIN_18
#define GPIO_UART_K230_TX_PIN                                     DL_GPIO_PIN_17
#define GPIO_UART_K230_IOMUX_RX                                  (IOMUX_PINCM44)
#define GPIO_UART_K230_IOMUX_TX                                  (IOMUX_PINCM43)
#define GPIO_UART_K230_IOMUX_RX_FUNC                   IOMUX_PINCM44_PF_UART2_RX
#define GPIO_UART_K230_IOMUX_TX_FUNC                   IOMUX_PINCM43_PF_UART2_TX
#define UART_K230_BAUD_RATE                                             (115200)
#define UART_K230_IBRD_40_MHZ_115200_BAUD                                   (21)
#define UART_K230_FBRD_40_MHZ_115200_BAUD                                   (45)
/* Defines for UART_WIT */
#define UART_WIT_INST                                                      UART0
#define UART_WIT_INST_FREQUENCY                                         40000000
#define UART_WIT_INST_IRQHandler                                UART0_IRQHandler
#define UART_WIT_INST_INT_IRQN                                    UART0_INT_IRQn
#define GPIO_UART_WIT_RX_PORT                                              GPIOA
#define GPIO_UART_WIT_RX_PIN                                      DL_GPIO_PIN_11
#define GPIO_UART_WIT_IOMUX_RX                                   (IOMUX_PINCM22)
#define GPIO_UART_WIT_IOMUX_RX_FUNC                    IOMUX_PINCM22_PF_UART0_RX
#define UART_WIT_BAUD_RATE                                              (115200)
#define UART_WIT_IBRD_40_MHZ_115200_BAUD                                    (21)
#define UART_WIT_FBRD_40_MHZ_115200_BAUD                                    (45)
/* Defines for UART_bujingA */
#define UART_bujingA_INST                                                  UART1
#define UART_bujingA_INST_FREQUENCY                                     40000000
#define UART_bujingA_INST_IRQHandler                            UART1_IRQHandler
#define UART_bujingA_INST_INT_IRQN                                UART1_INT_IRQn
#define GPIO_UART_bujingA_RX_PORT                                          GPIOB
#define GPIO_UART_bujingA_TX_PORT                                          GPIOB
#define GPIO_UART_bujingA_RX_PIN                                   DL_GPIO_PIN_5
#define GPIO_UART_bujingA_TX_PIN                                   DL_GPIO_PIN_4
#define GPIO_UART_bujingA_IOMUX_RX                               (IOMUX_PINCM18)
#define GPIO_UART_bujingA_IOMUX_TX                               (IOMUX_PINCM17)
#define GPIO_UART_bujingA_IOMUX_RX_FUNC                IOMUX_PINCM18_PF_UART1_RX
#define GPIO_UART_bujingA_IOMUX_TX_FUNC                IOMUX_PINCM17_PF_UART1_TX
#define UART_bujingA_BAUD_RATE                                            (9600)
#define UART_bujingA_IBRD_40_MHZ_9600_BAUD                                 (260)
#define UART_bujingA_FBRD_40_MHZ_9600_BAUD                                  (27)
/* Defines for UART_bujingB */
#define UART_bujingB_INST                                                  UART3
#define UART_bujingB_INST_FREQUENCY                                     80000000
#define UART_bujingB_INST_IRQHandler                            UART3_IRQHandler
#define UART_bujingB_INST_INT_IRQN                                UART3_INT_IRQn
#define GPIO_UART_bujingB_RX_PORT                                          GPIOB
#define GPIO_UART_bujingB_TX_PORT                                          GPIOB
#define GPIO_UART_bujingB_RX_PIN                                  DL_GPIO_PIN_13
#define GPIO_UART_bujingB_TX_PIN                                  DL_GPIO_PIN_12
#define GPIO_UART_bujingB_IOMUX_RX                               (IOMUX_PINCM30)
#define GPIO_UART_bujingB_IOMUX_TX                               (IOMUX_PINCM29)
#define GPIO_UART_bujingB_IOMUX_RX_FUNC                IOMUX_PINCM30_PF_UART3_RX
#define GPIO_UART_bujingB_IOMUX_TX_FUNC                IOMUX_PINCM29_PF_UART3_TX
#define UART_bujingB_BAUD_RATE                                            (9600)
#define UART_bujingB_IBRD_80_MHZ_9600_BAUD                                 (520)
#define UART_bujingB_FBRD_80_MHZ_9600_BAUD                                  (53)





/* Defines for DMA_CH_RX */
#define DMA_CH_RX_CHAN_ID                                                    (2)
#define UART_K230_INST_DMA_TRIGGER_0                         (DMA_UART2_RX_TRIG)
/* Defines for DMA_CH_TX */
#define DMA_CH_TX_CHAN_ID                                                    (1)
#define UART_K230_INST_DMA_TRIGGER_1                         (DMA_UART2_TX_TRIG)
/* Defines for DMA_WIT */
#define DMA_WIT_CHAN_ID                                                      (0)
#define UART_WIT_INST_DMA_TRIGGER                            (DMA_UART0_RX_TRIG)


/* Port definition for Pin Group BUZZ */
#define BUZZ_PORT                                                        (GPIOA)

/* Defines for Periph: GPIOA.3 with pinCMx 8 on package pin 43 */
#define BUZZ_Periph_PIN                                          (DL_GPIO_PIN_3)
#define BUZZ_Periph_IOMUX                                         (IOMUX_PINCM8)
/* Port definition for Pin Group GPIO_MPU6050 */
#define GPIO_MPU6050_PORT                                                (GPIOA)

/* Defines for PIN_INT: GPIOA.30 with pinCMx 5 on package pin 37 */
// pins affected by this interrupt request:["PIN_INT"]
#define GPIO_MPU6050_INT_IRQN                                   (GPIOA_INT_IRQn)
#define GPIO_MPU6050_INT_IIDX                   (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define GPIO_MPU6050_PIN_INT_IIDX                           (DL_GPIO_IIDX_DIO30)
#define GPIO_MPU6050_PIN_INT_PIN                                (DL_GPIO_PIN_30)
#define GPIO_MPU6050_PIN_INT_IOMUX                                (IOMUX_PINCM5)
/* Defines for Board: GPIOA.14 with pinCMx 36 on package pin 7 */
#define LED_Board_PORT                                                   (GPIOA)
#define LED_Board_PIN                                           (DL_GPIO_PIN_14)
#define LED_Board_IOMUX                                          (IOMUX_PINCM36)
/* Defines for RED: GPIOB.23 with pinCMx 51 on package pin 22 */
#define LED_RED_PORT                                                     (GPIOB)
#define LED_RED_PIN                                             (DL_GPIO_PIN_23)
#define LED_RED_IOMUX                                            (IOMUX_PINCM51)
/* Defines for BLUE: GPIOB.22 with pinCMx 50 on package pin 21 */
#define LED_BLUE_PORT                                                    (GPIOB)
#define LED_BLUE_PIN                                            (DL_GPIO_PIN_22)
#define LED_BLUE_IOMUX                                           (IOMUX_PINCM50)
/* Defines for KEY4: GPIOA.26 with pinCMx 59 on package pin 30 */
#define KEY_KEY4_PORT                                                    (GPIOA)
#define KEY_KEY4_PIN                                            (DL_GPIO_PIN_26)
#define KEY_KEY4_IOMUX                                           (IOMUX_PINCM59)
/* Defines for KEY2: GPIOB.24 with pinCMx 52 on package pin 23 */
#define KEY_KEY2_PORT                                                    (GPIOB)
#define KEY_KEY2_PIN                                            (DL_GPIO_PIN_24)
#define KEY_KEY2_IOMUX                                           (IOMUX_PINCM52)
/* Defines for KEY3: GPIOA.27 with pinCMx 60 on package pin 31 */
#define KEY_KEY3_PORT                                                    (GPIOA)
#define KEY_KEY3_PIN                                            (DL_GPIO_PIN_27)
#define KEY_KEY3_IOMUX                                           (IOMUX_PINCM60)
/* Port definition for Pin Group SPD_READER_A */
#define SPD_READER_A_PORT                                                (GPIOB)

/* Defines for FONT_LEFT_A: GPIOB.11 with pinCMx 28 on package pin 63 */
// pins affected by this interrupt request:["FONT_LEFT_A","FONT_RIGHT_A"]
#define SPD_READER_A_INT_IRQN                                   (GPIOB_INT_IRQn)
#define SPD_READER_A_INT_IIDX                   (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define SPD_READER_A_FONT_LEFT_A_IIDX                       (DL_GPIO_IIDX_DIO11)
#define SPD_READER_A_FONT_LEFT_A_PIN                            (DL_GPIO_PIN_11)
#define SPD_READER_A_FONT_LEFT_A_IOMUX                           (IOMUX_PINCM28)
/* Defines for FONT_RIGHT_A: GPIOB.9 with pinCMx 26 on package pin 61 */
#define SPD_READER_A_FONT_RIGHT_A_IIDX                       (DL_GPIO_IIDX_DIO9)
#define SPD_READER_A_FONT_RIGHT_A_PIN                            (DL_GPIO_PIN_9)
#define SPD_READER_A_FONT_RIGHT_A_IOMUX                          (IOMUX_PINCM26)
/* Port definition for Pin Group SPD_READER_B */
#define SPD_READER_B_PORT                                                (GPIOB)

/* Defines for FONT_LEFT_B: GPIOB.10 with pinCMx 27 on package pin 62 */
#define SPD_READER_B_FONT_LEFT_B_PIN                            (DL_GPIO_PIN_10)
#define SPD_READER_B_FONT_LEFT_B_IOMUX                           (IOMUX_PINCM27)
/* Defines for FONT_RIGHT_B: GPIOB.8 with pinCMx 25 on package pin 60 */
#define SPD_READER_B_FONT_RIGHT_B_PIN                            (DL_GPIO_PIN_8)
#define SPD_READER_B_FONT_RIGHT_B_IOMUX                          (IOMUX_PINCM25)
/* Port definition for Pin Group DIRC_CTRL */
#define DIRC_CTRL_PORT                                                   (GPIOA)

/* Defines for FONT_LEFT: GPIOA.23 with pinCMx 53 on package pin 24 */
#define DIRC_CTRL_FONT_LEFT_PIN                                 (DL_GPIO_PIN_23)
#define DIRC_CTRL_FONT_LEFT_IOMUX                                (IOMUX_PINCM53)
/* Defines for FONT_RIGHT: GPIOA.22 with pinCMx 47 on package pin 18 */
#define DIRC_CTRL_FONT_RIGHT_PIN                                (DL_GPIO_PIN_22)
#define DIRC_CTRL_FONT_RIGHT_IOMUX                               (IOMUX_PINCM47)
/* Defines for DAT: GPIOA.24 with pinCMx 54 on package pin 25 */
#define Serial_DAT_PORT                                                  (GPIOA)
#define Serial_DAT_PIN                                          (DL_GPIO_PIN_24)
#define Serial_DAT_IOMUX                                         (IOMUX_PINCM54)
/* Defines for CLK: GPIOB.20 with pinCMx 48 on package pin 19 */
#define Serial_CLK_PORT                                                  (GPIOB)
#define Serial_CLK_PIN                                          (DL_GPIO_PIN_20)
#define Serial_CLK_IOMUX                                         (IOMUX_PINCM48)





/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_MotorAFront_init(void);
void SYSCFG_DL_MotorBFront_init(void);
void SYSCFG_DL_I2C_OLED_init(void);
void SYSCFG_DL_UART_K230_init(void);
void SYSCFG_DL_UART_WIT_init(void);
void SYSCFG_DL_UART_bujingA_init(void);
void SYSCFG_DL_UART_bujingB_init(void);
void SYSCFG_DL_DMA_init(void);

void SYSCFG_DL_SYSTICK_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
