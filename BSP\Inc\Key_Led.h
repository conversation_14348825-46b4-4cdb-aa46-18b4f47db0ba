#ifndef __Key_Led_h
#define __Key_Led_h

#include "SysConfig.h"

/*LED BOARD*/
#define LED_BOARD_ON()     DL_GPIO_setPins(GPIOA, LED_Board_PIN)
#define LED_BOARD_OFF()    DL_GPIO_clearPins(GPIOA, LED_Board_PIN)
#define LED_BOARD_TOGGLE() DL_GPIO_togglePins(GPIOA, LED_Board_PIN)

/*LED RED*/
#define LED_RED_ON()  DL_GPIO_setPins(LED_PORT, LED_RED_PIN)
#define LED_RED_OFF() DL_GPIO_clearPins(LED_PORT, LED_RED_PIN)
#define LED_RED_TOGGLE() DL_GPIO_togglePins(LED_PORT, LED_RED_PRED)
/*LED BLUE*/
#define LED_BLUE_ON()     DL_GPIO_setPins(LED_PORT, LED_BLUE_PIN)
#define LED_BLUE_OFF()    DL_GPIO_clearPins(LED_PORT, LED_BLUE_PIN)
#define LED_BLUE_TOGGLE() DL_GPIO_togglePins(LED_PORT, LED_BLUE_PIN)

/*BUZZ*/
#define BUZZ_ON()  DL_GPIO_setPins(BUZZ_PORT, BUZZ_Periph_PIN)
#define BUZZ_OFF() DL_GPIO_clearPins(BUZZ_PORT, BUZZ_Periph_PIN)

uint8_t Key_Read(void);

#endif
