################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../BSP/Src/Key_Led.c \
../BSP/Src/Motor.c \
../BSP/Src/OLED.c \
../BSP/Src/OLED_Font.c \
../BSP/Src/PID.c \
../BSP/Src/PID_IQMath.c \
../BSP/Src/Serial.c \
../BSP/Src/SysTick.c \
../BSP/Src/Task.c \
../BSP/Src/Tracker.c \
../BSP/Src/need.c \
../BSP/Src/wit.c 

C_DEPS += \
./BSP/Src/Key_Led.d \
./BSP/Src/Motor.d \
./BSP/Src/OLED.d \
./BSP/Src/OLED_Font.d \
./BSP/Src/PID.d \
./BSP/Src/PID_IQMath.d \
./BSP/Src/Serial.d \
./BSP/Src/SysTick.d \
./BSP/Src/Task.d \
./BSP/Src/Tracker.d \
./BSP/Src/need.d \
./BSP/Src/wit.d 

OBJS += \
./BSP/Src/Key_Led.o \
./BSP/Src/Motor.o \
./BSP/Src/OLED.o \
./BSP/Src/OLED_Font.o \
./BSP/Src/PID.o \
./BSP/Src/PID_IQMath.o \
./BSP/Src/Serial.o \
./BSP/Src/SysTick.o \
./BSP/Src/Task.o \
./BSP/Src/Tracker.o \
./BSP/Src/need.o \
./BSP/Src/wit.o 

OBJS__QUOTED += \
"BSP\Src\Key_Led.o" \
"BSP\Src\Motor.o" \
"BSP\Src\OLED.o" \
"BSP\Src\OLED_Font.o" \
"BSP\Src\PID.o" \
"BSP\Src\PID_IQMath.o" \
"BSP\Src\Serial.o" \
"BSP\Src\SysTick.o" \
"BSP\Src\Task.o" \
"BSP\Src\Tracker.o" \
"BSP\Src\need.o" \
"BSP\Src\wit.o" 

C_DEPS__QUOTED += \
"BSP\Src\Key_Led.d" \
"BSP\Src\Motor.d" \
"BSP\Src\OLED.d" \
"BSP\Src\OLED_Font.d" \
"BSP\Src\PID.d" \
"BSP\Src\PID_IQMath.d" \
"BSP\Src\Serial.d" \
"BSP\Src\SysTick.d" \
"BSP\Src\Task.d" \
"BSP\Src\Tracker.d" \
"BSP\Src\need.d" \
"BSP\Src\wit.d" 

C_SRCS__QUOTED += \
"../BSP/Src/Key_Led.c" \
"../BSP/Src/Motor.c" \
"../BSP/Src/OLED.c" \
"../BSP/Src/OLED_Font.c" \
"../BSP/Src/PID_IQMath.c" \
"../BSP/Src/Serial.c" \
"../BSP/Src/SysTick.c" \
"../BSP/Src/Task.c" \
"../BSP/Src/Tracker.c" \
"../BSP/Src/need.c" \
"../BSP/Src/wit.c" 


