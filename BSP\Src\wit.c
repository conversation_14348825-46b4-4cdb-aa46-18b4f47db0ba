#include "wit.h"

uint8_t wit_dmaBuffer[33];

WIT_Data_t wit_data;

static PID_Def_t Angle_PID_Instance;  // 新增：角度PID实例

void WIT_Init(void)
{
    DL_DMA_setSrcAddr(DMA, DMA_WIT_CHAN_ID, (uint32_t)(&UART_WIT_INST->RXDATA));
    DL_DMA_setDestAddr(DMA, DMA_WIT_CHAN_ID, (uint32_t) &wit_dmaBuffer[0]);
    DL_DMA_setTransferSize(DMA, DMA_WIT_CHAN_ID, 32);
    DL_DMA_enableChannel(DMA, DMA_WIT_CHAN_ID);

    NVIC_EnableIRQ(UART_WIT_INST_INT_IRQN);

    // 新增：初始化角度PID
    PID_Init(&Angle_PID_Instance);
    PID_SetParams(&Angle_PID_Instance, 0.4f, 0.0f, 0.8f);
}

// 重构wit_direct函数（保持接口不变）
bool wit_direct(float target_angle, float *steering_correction) {
    if (steering_correction == NULL) return false;

    // 数据有效性检查（新增 - 防止NaN值导致系统卡死）
    if (!isfinite(target_angle) || !isfinite(wit_data.yaw)) {
        *steering_correction = 0.0f;  // 安全输出
        return false;  // 与项目错误处理风格一致
    }

    // 角度溢出处理（保持现有逻辑）
    float angle_error = target_angle - wit_data.yaw;
    if (angle_error > 180.0f) angle_error -= 360.0f;
    else if (angle_error < -180.0f) angle_error += 360.0f;

    // 角度误差有效性检查（新增保护 - 防止计算结果异常）
    if (!isfinite(angle_error)) {
        *steering_correction = 0.0f;
        return false;
    }

    // 死区判断（新增精确控制）
    #define ANGLE_DEAD_ZONE 0.5f
    if (fabs(angle_error) <= ANGLE_DEAD_ZONE) {
        *steering_correction = 0.0f;
        return true;
    }

    // PID控制（替换原有比例控制）
    Angle_PID_Instance.Target = target_angle;
    Angle_PID_Instance.Acutal_Now = wit_data.yaw;
    Angle_PID_Instance.Err_Last = Angle_PID_Instance.Err_Now;
    Angle_PID_Instance.Err_Now = angle_error;
    PID_AProsc(&Angle_PID_Instance);

    // 输出限幅（保持安全机制）
    float correction_float = Angle_PID_Instance.Out;
    #define MAX_STEERING_CORRECTION 15.0f
    if (correction_float > MAX_STEERING_CORRECTION) {
        correction_float = MAX_STEERING_CORRECTION;
    } else if (correction_float < -MAX_STEERING_CORRECTION) {
        correction_float = -MAX_STEERING_CORRECTION;
    }

    *steering_correction = correction_float;
    return true;
}


// 外部变量声明
extern bool Data_wit_ControlEnabled;
extern float Data_wit_UserTarget;

void WIT_SetTargetAngle(float angle)
{
    Data_wit_UserTarget = angle;
    Data_wit_ControlEnabled = true;
}

void WIT_CancelTargetControl(void)
{
    Data_wit_ControlEnabled = false;
}

bool WIT_IsTargetControlActive(void)
{
    return Data_wit_ControlEnabled;
}

float WIT_GetCurrentTarget(void)
{
    return Data_wit_UserTarget;
}

