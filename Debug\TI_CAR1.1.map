******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Mon Jul 28 22:41:45 2025

OUTPUT FILE NAME:   <TI_CAR1.1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004c61


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006278  00019d88  R  X
  SRAM                  20200000   00008000  00000a26  000075da  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006278   00006278    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000056e0   000056e0    r-x .text
  000057a0    000057a0    00000a80   00000a80    r-- .rodata
  00006220    00006220    00000058   00000058    r-- .cinit
20200000    20200000    00000826   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    00000364   00000000    rw- .bss
  20200764    20200764    000000c2   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000056e0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000368     Interrupt.o (.text.UART1_IRQHandler)
                  00000df8    000002e0     Task_App.o (.text.Task_AutoRecover)
                  000010d8    00000254     wit.o (.text.wit_direct)
                  0000132c    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000154c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00001728    000001b0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000018d8    000001b0     Task.o (.text.Task_Start)
                  00001a88    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001c1a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001c1c    00000158     Tracker.o (.text.Tracker_Read)
                  00001d74    00000144     PID.o (.text.PID_SProsc)
                  00001eb8    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001ff4    00000134            : qsort.c.obj (.text.qsort)
                  00002128    00000130     OLED.o (.text.OLED_ShowChar)
                  00002258    0000012c     PID.o (.text.PID_AProsc)
                  00002384    00000120     Task_App.o (.text.Task_Motor_PID)
                  000024a4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  000025c4    00000110     OLED.o (.text.OLED_Init)
                  000026d4    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  000027e0    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000028ec    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000029f0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002ad4    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002bb0    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002c88    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002d60    000000d0     Task_App.o (.text.Task_Init)
                  00002e30    000000b4     Task.o (.text.Task_Add)
                  00002ee4    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002f94    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00003036    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003038    0000009c     Motor.o (.text.Motor_SetDuty)
                  000030d4    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  0000316c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  000031f8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003284    0000008c     Task_App.o (.text.Task_wit)
                  00003310    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000339c    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00003420    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000034a4    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003520    00000074     Motor.o (.text.Motor_SetDirc)
                  00003594    00000074     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  00003608    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000367c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003680    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000036f4    00000074     SysTick.o (.text.delay_us)
                  00003768    0000006e     OLED.o (.text.OLED_ShowString)
                  000037d6    00000002     --HOLE-- [fill = 0]
                  000037d8    0000006c     Motor.o (.text.Motor_Start)
                  00003844    0000006c     Task_App.o (.text.Task_OLED)
                  000038b0    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000391a    00000002     --HOLE-- [fill = 0]
                  0000391c    00000068     Task_App.o (.text.Task_Key)
                  00003984    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000039ec    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003a52    00000002     --HOLE-- [fill = 0]
                  00003a54    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00003ab8    00000064     wit.o (.text.WIT_Init)
                  00003b1c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003b7e    00000002     --HOLE-- [fill = 0]
                  00003b80    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003be2    00000002     --HOLE-- [fill = 0]
                  00003be4    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003c44    00000060     Key_Led.o (.text.Key_Read)
                  00003ca4    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003d04    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003d62    00000002     --HOLE-- [fill = 0]
                  00003d64    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003dc0    0000005c     Task_App.o (.text.Task_Tracker)
                  00003e1c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003e78    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00003ed4    00000058     Serial.o (.text.Serial_Init)
                  00003f2c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003f84    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003fdc    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00004032    00000002     --HOLE-- [fill = 0]
                  00004034    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00004088    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000040da    00000002     --HOLE-- [fill = 0]
                  000040dc    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  0000412c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000417c    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000041c8    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004214    0000004c     OLED.o (.text.OLED_Printf)
                  00004260    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000042aa    00000002     --HOLE-- [fill = 0]
                  000042ac    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000042f4    00000048     Motor.o (.text.Motor_GetSpeed)
                  0000433c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004380    00000044     OLED.o (.text.mspm0_i2c_disable)
                  000043c4    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00004406    00000002     --HOLE-- [fill = 0]
                  00004408    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004448    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004488    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000044c8    0000003e     Task.o (.text.Task_CMP)
                  00004506    00000002     --HOLE-- [fill = 0]
                  00004508    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004544    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004580    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000045bc    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  000045f8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004634    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004670    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000046ac    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000046e6    00000002     --HOLE-- [fill = 0]
                  000046e8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004722    00000002     --HOLE-- [fill = 0]
                  00004724    00000038     Interrupt.o (.text.Interrupt_Init)
                  0000475c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00004794    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000047c8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000047fc    00000034     Task_App.o (.text.Task_IdleFunction)
                  00004830    00000030     Interrupt.o (.text.DL_DMA_setTransferSize)
                  00004860    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00004890    00000030     wit.o (.text.DL_DMA_setTransferSize)
                  000048c0    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  000048f0    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00004920    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004950    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  0000497c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000049a8    0000002c     wit.o (.text.__NVIC_EnableIRQ)
                  000049d4    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004a00    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00004a2c    0000002a     PID.o (.text.PID_Init)
                  00004a56    00000028     OLED.o (.text.DL_Common_updateReg)
                  00004a7e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004aa6    00000002     --HOLE-- [fill = 0]
                  00004aa8    00000028     Interrupt.o (.text.DL_DMA_setDestAddr)
                  00004ad0    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00004af8    00000028     wit.o (.text.DL_DMA_setDestAddr)
                  00004b20    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00004b48    00000028     wit.o (.text.DL_DMA_setSrcAddr)
                  00004b70    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004b98    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004bc0    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004be8    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00004c10    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004c38    00000028     SysTick.o (.text.SysTick_Increasment)
                  00004c60    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004c88    00000026     Interrupt.o (.text.DL_DMA_disableChannel)
                  00004cae    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00004cd4    00000026     Interrupt.o (.text.DL_DMA_enableChannel)
                  00004cfa    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00004d20    00000026     wit.o (.text.DL_DMA_enableChannel)
                  00004d46    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004d6c    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004d92    00000002     --HOLE-- [fill = 0]
                  00004d94    00000024     Interrupt.o (.text.DL_DMA_getTransferSize)
                  00004db8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004ddc    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00004e00    00000022     PID.o (.text.PID_SetParams)
                  00004e22    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004e44    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00004e64    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004e84    00000020     SysTick.o (.text.Delay)
                  00004ea4    00000020     main.o (.text.main)
                  00004ec4    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00004ee2    00000002     --HOLE-- [fill = 0]
                  00004ee4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004f02    00000002     --HOLE-- [fill = 0]
                  00004f04    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00004f20    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00004f3c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004f58    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00004f74    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004f90    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004fac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004fc8    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00004fe4    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00005000    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  0000501c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005038    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005054    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005070    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000508c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000050a8    0000001c     wit.o (.text.WIT_SetTargetAngle)
                  000050c4    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000050dc    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000050f4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000510c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005124    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000513c    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00005154    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000516c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00005184    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000519c    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000051b4    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000051cc    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  000051e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000051fc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005214    00000018     Task_App.o (.text.DL_GPIO_togglePins)
                  0000522c    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00005244    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  0000525c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005274    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  0000528c    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000052a4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000052bc    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000052d4    00000018     OLED.o (.text.DL_I2C_reset)
                  000052ec    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005304    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  0000531c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00005334    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  0000534c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005364    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  0000537c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00005394    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000053ac    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000053c4    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  000053dc    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000053f4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  0000540c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005424    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000543c    00000018     Interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  00005454    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0000546c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00005484    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  0000549c    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  000054b4    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000054ca    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000054e0    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000054f6    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  0000550c    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005522    00000016     wit.o (.text.__f64_bits_as_u64)
                  00005538    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000554e    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00005562    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00005576    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  0000558a    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000559e    00000002     --HOLE-- [fill = 0]
                  000055a0    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000055b4    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000055c8    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000055dc    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000055f0    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005604    00000014     Interrupt.o (.text.DL_UART_receiveData)
                  00005618    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  0000562c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005640    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005652    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005664    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005676    00000002     --HOLE-- [fill = 0]
                  00005678    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005688    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005698    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000056a8    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000056b8    00000010     wit.o (.text.WIT_IsTargetControlActive)
                  000056c8    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000056d8    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000056e6    0000000e     wit.o (.text.__f32_bits_as_u32)
                  000056f4    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005702    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00005710    0000000c     SysTick.o (.text.Sys_GetTick)
                  0000571c    0000000c     wit.o (.text.WIT_CancelTargetControl)
                  00005728    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005732    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000573c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  0000574c    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005756    0000000a            : vsprintf.c.obj (.text._outc)
                  00005760    00000008     Interrupt.o (.text.SysTick_Handler)
                  00005768    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005770    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00005778    00000006     libc.a : exit.c.obj (.text:abort)
                  0000577e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00005782    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005786    00000002     --HOLE-- [fill = 0]
                  00005788    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005798    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000579c    00000004     --HOLE-- [fill = 0]

.cinit     0    00006220    00000058     
                  00006220    00000034     (.cinit..data.load) [load image, compression = lzss]
                  00006254    0000000c     (__TI_handler_table)
                  00006260    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006268    00000010     (__TI_cinit_table)

.rodata    0    000057a0    00000a80     
                  000057a0    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00005d90    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00005fb8    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00005fc0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000060c1    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00006102    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00006104    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000612c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00006144    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000615c    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00006174    00000013     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00006187    00000012     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00006199    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000061aa    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000061bb    00000010     Task_App.o (.rodata.str1.14074990341397557290.1)
                  000061cb    0000000c     Task_App.o (.rodata.str1.11683036942922059812.1)
                  000061d7    0000000b     Task_App.o (.rodata.str1.492715258893803702.1)
                  000061e2    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  000061ec    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  000061f6    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  000061fe    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00006204    00000005     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00006209    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000620d    00000004     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00006211    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00006214    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00006216    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00006218    00000008     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    00000364     UNINITIALIZED
                  20200400    00000200     (.common:Serial_RxData)
                  20200600    000000f0     Task.o (.bss.Task_Schedule)
                  202006f0    0000002c     wit.o (.bss.Angle_PID_Instance)
                  2020071c    00000021     (.common:wit_dmaBuffer)
                  2020073d    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  2020073e    00000001     (.common:ret)
                  2020073f    00000001     --HOLE--
                  20200740    00000020     (.common:wit_data)
                  20200760    00000004     (.common:ExISR_Flag)

.data      0    20200764    000000c2     UNINITIALIZED
                  20200764    00000040     Motor.o (.data.Motor_Font_Left)
                  202007a4    00000040     Motor.o (.data.Motor_Font_Right)
                  202007e4    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202007ec    00000008     Task_App.o (.data.Motor)
                  202007f4    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202007f8    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202007fc    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  20200800    00000004     Task_App.o (.data.Data_wit_Offset)
                  20200804    00000004     Task_App.o (.data.Data_wit_Target)
                  20200808    00000004     Task_App.o (.data.Data_wit_UserTarget)
                  2020080c    00000004     Task_App.o (.data.Task_AutoRecover.line_found_time)
                  20200810    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200814    00000004     SysTick.o (.data.delayTick)
                  20200818    00000004     Task_App.o (.data.lost_time)
                  2020081c    00000004     SysTick.o (.data.uwTick)
                  20200820    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200822    00000001     Task_App.o (.data.Data_wit_ControlEnabled)
                  20200823    00000001     Task.o (.data.Task_Num)
                  20200824    00000001     Task_App.o (.data.current_path)
                  20200825    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3110    149       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3150    341       0      
                                                               
    .\APP\Src\
       Task_App.o                     1752    103       53     
       Interrupt.o                    1526    0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         3278    103       58     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1854    0         0      
       wit.o                          998     0         109    
       Task.o                         674     0         241    
       Serial.o                       292     0         512    
       PID.o                          700     0         0      
       Motor.o                        520     0         128    
       Tracker.o                      410     0         1      
       SysTick.o                      200     0         8      
       Key_Led.o                      118     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5766    2072      999    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1136    0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5736    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2766    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       88        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   22196   2960      2597   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006268 records: 2, size/record: 8, table size: 16
	.data: load addr=00006220, load size=00000034 bytes, run addr=20200764, run size=000000c2 bytes, compression=lzss
	.bss: load addr=00006260, load size=00000008 bytes, run addr=20200400, run size=00000364 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006254 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001a89     0000573c     0000573a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004c61     00005788     00005782   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000367d  ADC0_IRQHandler                      
0000367d  ADC1_IRQHandler                      
0000367d  AES_IRQHandler                       
0000577e  C$$EXIT                              
0000367d  CANFD0_IRQHandler                    
0000367d  DAC0_IRQHandler                      
00005729  DL_Common_delayCycles                
0000417d  DL_DMA_initChannel                   
00003d05  DL_I2C_fillControllerTXFIFO          
00004d6d  DL_I2C_setClockConfig                
00002ad5  DL_SYSCTL_configSYSPLL               
00003a55  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000433d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000028ed  DL_Timer_initFourCCPWMMode           
00005055  DL_Timer_setCaptCompUpdateMethod     
00005395  DL_Timer_setCaptureCompareOutCtl     
00005689  DL_Timer_setCaptureCompareValue      
00005071  DL_Timer_setClockConfig              
00004035  DL_UART_drainRXFIFO                  
000042ad  DL_UART_init                         
00005641  DL_UART_setClockConfig               
0000367d  DMA_IRQHandler                       
202007f4  Data_MotorEncoder                    
202007f8  Data_Motor_TarSpeed                  
202007e4  Data_Tracker_Input                   
202007fc  Data_Tracker_Offset                  
20200822  Data_wit_ControlEnabled              
20200800  Data_wit_Offset                      
20200804  Data_wit_Target                      
20200808  Data_wit_UserTarget                  
0000367d  Default_Handler                      
00004e85  Delay                                
20200760  ExISR_Flag                           
0000367d  GROUP0_IRQHandler                    
00002ee5  GROUP1_IRQHandler                    
0000577f  HOSTexit                             
0000367d  HardFault_Handler                    
0000367d  I2C0_IRQHandler                      
0000367d  I2C1_IRQHandler                      
000038b1  I2C_OLED_Clear                       
000045bd  I2C_OLED_Set_Pos                     
000030d5  I2C_OLED_WR_Byte                     
00003be5  I2C_OLED_i2c_sda_unlock              
00004725  Interrupt_Init                       
00003c45  Key_Read                             
202007ec  Motor                                
20200764  Motor_Font_Left                      
202007a4  Motor_Font_Right                     
000042f5  Motor_GetSpeed                       
00003039  Motor_SetDuty                        
000037d9  Motor_Start                          
0000367d  NMI_Handler                          
000025c5  OLED_Init                            
00004215  OLED_Printf                          
00002129  OLED_ShowChar                        
00003769  OLED_ShowString                      
00002259  PID_AProsc                           
00004a2d  PID_Init                             
00001d75  PID_SProsc                           
00004e01  PID_SetParams                        
0000367d  PendSV_Handler                       
0000367d  RTC_IRQHandler                       
00005783  Reset_Handler                        
0000367d  SPI0_IRQHandler                      
0000367d  SPI1_IRQHandler                      
0000367d  SVC_Handler                          
000048c1  SYSCFG_DL_DMA_CH_RX_init             
0000546d  SYSCFG_DL_DMA_CH_TX_init             
00005485  SYSCFG_DL_DMA_WIT_init               
00005699  SYSCFG_DL_DMA_init                   
00001729  SYSCFG_DL_GPIO_init                  
00003ca5  SYSCFG_DL_I2C_OLED_init              
0000316d  SYSCFG_DL_MotorFront_init            
00003d65  SYSCFG_DL_SYSCTL_init                
000056a9  SYSCFG_DL_SYSTICK_init               
0000339d  SYSCFG_DL_UART0_init                 
00003595  SYSCFG_DL_UART_WIT_init              
00004c11  SYSCFG_DL_init                       
000031f9  SYSCFG_DL_initPower                  
00003ed5  Serial_Init                          
20200400  Serial_RxData                        
00005761  SysTick_Handler                      
00004c39  SysTick_Increasment                  
00005711  Sys_GetTick                          
0000367d  TIMA0_IRQHandler                     
0000367d  TIMA1_IRQHandler                     
0000367d  TIMG0_IRQHandler                     
0000367d  TIMG12_IRQHandler                    
0000367d  TIMG6_IRQHandler                     
0000367d  TIMG7_IRQHandler                     
0000367d  TIMG8_IRQHandler                     
00005653  TI_memcpy_small                      
00005703  TI_memset_small                      
00002e31  Task_Add                             
00000df9  Task_AutoRecover                     
000047fd  Task_IdleFunction                    
00002d61  Task_Init                            
0000391d  Task_Key                             
00002385  Task_Motor_PID                       
00003845  Task_OLED                            
000018d9  Task_Start                           
00003dc1  Task_Tracker                         
00003285  Task_wit                             
00001c1d  Tracker_Read                         
0000367d  UART0_IRQHandler                     
00000a91  UART1_IRQHandler                     
0000367d  UART2_IRQHandler                     
0000367d  UART3_IRQHandler                     
0000571d  WIT_CancelTargetControl              
00003ab9  WIT_Init                             
000056b9  WIT_IsTargetControlActive            
000050a9  WIT_SetTargetAngle                   
000026d5  _IQ24div                             
00004951  _IQ24mpy                             
000048f1  _IQ24toF                             
000060c1  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006268  __TI_CINIT_Base                      
00006278  __TI_CINIT_Limit                     
00006278  __TI_CINIT_Warm                      
00006254  __TI_Handler_Table_Base              
00006260  __TI_Handler_Table_Limit             
00004671  __TI_auto_init_nobinit_nopinit       
000034a5  __TI_decompress_lzss                 
00005665  __TI_decompress_none                 
00003f2d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005539  __TI_zero_init_nomemset              
00001a93  __adddf3                             
00002c93  __addsf3                             
00005fc0  __aeabi_ctype_table_                 
00005fc0  __aeabi_ctype_table_C                
00003681  __aeabi_d2f                          
00004261  __aeabi_d2iz                         
000043c5  __aeabi_d2uiz                        
00001a93  __aeabi_dadd                         
00003b1d  __aeabi_dcmpeq                       
00003b59  __aeabi_dcmpge                       
00003b6d  __aeabi_dcmpgt                       
00003b45  __aeabi_dcmple                       
00003b31  __aeabi_dcmplt                       
000027e1  __aeabi_ddiv                         
000029f1  __aeabi_dmul                         
00001a89  __aeabi_dsub                         
20200810  __aeabi_errno                        
00005769  __aeabi_errno_addr                   
00004449  __aeabi_f2d                          
0000475d  __aeabi_f2iz                         
00002c93  __aeabi_fadd                         
00003b81  __aeabi_fcmpeq                       
00003bbd  __aeabi_fcmpge                       
00003bd1  __aeabi_fcmpgt                       
00003ba9  __aeabi_fcmple                       
00003b95  __aeabi_fcmplt                       
00003311  __aeabi_fmul                         
00002c89  __aeabi_fsub                         
000049d5  __aeabi_i2d                          
000045f9  __aeabi_i2f                          
00003fdd  __aeabi_idiv                         
00001c1b  __aeabi_idiv0                        
00003fdd  __aeabi_idivmod                      
00003037  __aeabi_ldiv0                        
00004ee5  __aeabi_llsl                         
00004ddd  __aeabi_lmul                         
00005771  __aeabi_memcpy                       
00005771  __aeabi_memcpy4                      
00005771  __aeabi_memcpy8                      
000056d9  __aeabi_memset                       
000056d9  __aeabi_memset4                      
000056d9  __aeabi_memset8                      
00004409  __aeabi_uidiv                        
00004409  __aeabi_uidivmod                     
00005619  __aeabi_uldivmod                     
00004ee5  __ashldi3                            
ffffffff  __binit__                            
00003985  __cmpdf2                             
000046ad  __cmpsf2                             
000027e1  __divdf3                             
00003985  __eqdf2                              
000046ad  __eqsf2                              
00004449  __extendsfdf2                        
00004261  __fixdfsi                            
0000475d  __fixsfsi                            
000043c5  __fixunsdfsi                         
000049d5  __floatsidf                          
000045f9  __floatsisf                          
00003609  __gedf2                              
00004635  __gesf2                              
00003609  __gtdf2                              
00004635  __gtsf2                              
00003985  __ledf2                              
000046ad  __lesf2                              
00003985  __ltdf2                              
000046ad  __ltsf2                              
UNDEFED   __mpu_init                           
000029f1  __muldf3                             
00004ddd  __muldi3                             
000046e9  __muldsi3                            
00003311  __mulsf3                             
00003985  __nedf2                              
000046ad  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001a89  __subdf3                             
00002c89  __subsf3                             
00003681  __truncdfsf2                         
00002f95  __udivmoddi4                         
00004c61  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
00005799  _system_pre_init                     
00005779  abort                                
00005d90  asc2_0806                            
000057a0  asc2_1608                            
00004489  atoi                                 
ffffffff  binit                                
20200814  delayTick                            
000036f5  delay_us                             
20200825  enable_group1_irq                    
00003e1d  frexp                                
00003e1d  frexpl                               
00000000  interruptVectors                     
00002bb1  ldexp                                
00002bb1  ldexpl                               
00004ea5  main                                 
00004e23  memccpy                              
00001ff5  qsort                                
2020073e  ret                                  
00002bb1  scalbn                               
00002bb1  scalbnl                              
2020081c  uwTick                               
00004a01  vsprintf                             
000056c9  wcslen                               
20200740  wit_data                             
000010d9  wit_direct                           
2020071c  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000a91  UART1_IRQHandler                     
00000df9  Task_AutoRecover                     
000010d9  wit_direct                           
00001729  SYSCFG_DL_GPIO_init                  
000018d9  Task_Start                           
00001a89  __aeabi_dsub                         
00001a89  __subdf3                             
00001a93  __adddf3                             
00001a93  __aeabi_dadd                         
00001c1b  __aeabi_idiv0                        
00001c1d  Tracker_Read                         
00001d75  PID_SProsc                           
00001ff5  qsort                                
00002129  OLED_ShowChar                        
00002259  PID_AProsc                           
00002385  Task_Motor_PID                       
000025c5  OLED_Init                            
000026d5  _IQ24div                             
000027e1  __aeabi_ddiv                         
000027e1  __divdf3                             
000028ed  DL_Timer_initFourCCPWMMode           
000029f1  __aeabi_dmul                         
000029f1  __muldf3                             
00002ad5  DL_SYSCTL_configSYSPLL               
00002bb1  ldexp                                
00002bb1  ldexpl                               
00002bb1  scalbn                               
00002bb1  scalbnl                              
00002c89  __aeabi_fsub                         
00002c89  __subsf3                             
00002c93  __addsf3                             
00002c93  __aeabi_fadd                         
00002d61  Task_Init                            
00002e31  Task_Add                             
00002ee5  GROUP1_IRQHandler                    
00002f95  __udivmoddi4                         
00003037  __aeabi_ldiv0                        
00003039  Motor_SetDuty                        
000030d5  I2C_OLED_WR_Byte                     
0000316d  SYSCFG_DL_MotorFront_init            
000031f9  SYSCFG_DL_initPower                  
00003285  Task_wit                             
00003311  __aeabi_fmul                         
00003311  __mulsf3                             
0000339d  SYSCFG_DL_UART0_init                 
000034a5  __TI_decompress_lzss                 
00003595  SYSCFG_DL_UART_WIT_init              
00003609  __gedf2                              
00003609  __gtdf2                              
0000367d  ADC0_IRQHandler                      
0000367d  ADC1_IRQHandler                      
0000367d  AES_IRQHandler                       
0000367d  CANFD0_IRQHandler                    
0000367d  DAC0_IRQHandler                      
0000367d  DMA_IRQHandler                       
0000367d  Default_Handler                      
0000367d  GROUP0_IRQHandler                    
0000367d  HardFault_Handler                    
0000367d  I2C0_IRQHandler                      
0000367d  I2C1_IRQHandler                      
0000367d  NMI_Handler                          
0000367d  PendSV_Handler                       
0000367d  RTC_IRQHandler                       
0000367d  SPI0_IRQHandler                      
0000367d  SPI1_IRQHandler                      
0000367d  SVC_Handler                          
0000367d  TIMA0_IRQHandler                     
0000367d  TIMA1_IRQHandler                     
0000367d  TIMG0_IRQHandler                     
0000367d  TIMG12_IRQHandler                    
0000367d  TIMG6_IRQHandler                     
0000367d  TIMG7_IRQHandler                     
0000367d  TIMG8_IRQHandler                     
0000367d  UART0_IRQHandler                     
0000367d  UART2_IRQHandler                     
0000367d  UART3_IRQHandler                     
00003681  __aeabi_d2f                          
00003681  __truncdfsf2                         
000036f5  delay_us                             
00003769  OLED_ShowString                      
000037d9  Motor_Start                          
00003845  Task_OLED                            
000038b1  I2C_OLED_Clear                       
0000391d  Task_Key                             
00003985  __cmpdf2                             
00003985  __eqdf2                              
00003985  __ledf2                              
00003985  __ltdf2                              
00003985  __nedf2                              
00003a55  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003ab9  WIT_Init                             
00003b1d  __aeabi_dcmpeq                       
00003b31  __aeabi_dcmplt                       
00003b45  __aeabi_dcmple                       
00003b59  __aeabi_dcmpge                       
00003b6d  __aeabi_dcmpgt                       
00003b81  __aeabi_fcmpeq                       
00003b95  __aeabi_fcmplt                       
00003ba9  __aeabi_fcmple                       
00003bbd  __aeabi_fcmpge                       
00003bd1  __aeabi_fcmpgt                       
00003be5  I2C_OLED_i2c_sda_unlock              
00003c45  Key_Read                             
00003ca5  SYSCFG_DL_I2C_OLED_init              
00003d05  DL_I2C_fillControllerTXFIFO          
00003d65  SYSCFG_DL_SYSCTL_init                
00003dc1  Task_Tracker                         
00003e1d  frexp                                
00003e1d  frexpl                               
00003ed5  Serial_Init                          
00003f2d  __TI_ltoa                            
00003fdd  __aeabi_idiv                         
00003fdd  __aeabi_idivmod                      
00004035  DL_UART_drainRXFIFO                  
0000417d  DL_DMA_initChannel                   
00004215  OLED_Printf                          
00004261  __aeabi_d2iz                         
00004261  __fixdfsi                            
000042ad  DL_UART_init                         
000042f5  Motor_GetSpeed                       
0000433d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000043c5  __aeabi_d2uiz                        
000043c5  __fixunsdfsi                         
00004409  __aeabi_uidiv                        
00004409  __aeabi_uidivmod                     
00004449  __aeabi_f2d                          
00004449  __extendsfdf2                        
00004489  atoi                                 
000045bd  I2C_OLED_Set_Pos                     
000045f9  __aeabi_i2f                          
000045f9  __floatsisf                          
00004635  __gesf2                              
00004635  __gtsf2                              
00004671  __TI_auto_init_nobinit_nopinit       
000046ad  __cmpsf2                             
000046ad  __eqsf2                              
000046ad  __lesf2                              
000046ad  __ltsf2                              
000046ad  __nesf2                              
000046e9  __muldsi3                            
00004725  Interrupt_Init                       
0000475d  __aeabi_f2iz                         
0000475d  __fixsfsi                            
000047fd  Task_IdleFunction                    
000048c1  SYSCFG_DL_DMA_CH_RX_init             
000048f1  _IQ24toF                             
00004951  _IQ24mpy                             
000049d5  __aeabi_i2d                          
000049d5  __floatsidf                          
00004a01  vsprintf                             
00004a2d  PID_Init                             
00004c11  SYSCFG_DL_init                       
00004c39  SysTick_Increasment                  
00004c61  _c_int00_noargs                      
00004d6d  DL_I2C_setClockConfig                
00004ddd  __aeabi_lmul                         
00004ddd  __muldi3                             
00004e01  PID_SetParams                        
00004e23  memccpy                              
00004e85  Delay                                
00004ea5  main                                 
00004ee5  __aeabi_llsl                         
00004ee5  __ashldi3                            
00005055  DL_Timer_setCaptCompUpdateMethod     
00005071  DL_Timer_setClockConfig              
000050a9  WIT_SetTargetAngle                   
00005395  DL_Timer_setCaptureCompareOutCtl     
0000546d  SYSCFG_DL_DMA_CH_TX_init             
00005485  SYSCFG_DL_DMA_WIT_init               
00005539  __TI_zero_init_nomemset              
00005619  __aeabi_uldivmod                     
00005641  DL_UART_setClockConfig               
00005653  TI_memcpy_small                      
00005665  __TI_decompress_none                 
00005689  DL_Timer_setCaptureCompareValue      
00005699  SYSCFG_DL_DMA_init                   
000056a9  SYSCFG_DL_SYSTICK_init               
000056b9  WIT_IsTargetControlActive            
000056c9  wcslen                               
000056d9  __aeabi_memset                       
000056d9  __aeabi_memset4                      
000056d9  __aeabi_memset8                      
00005703  TI_memset_small                      
00005711  Sys_GetTick                          
0000571d  WIT_CancelTargetControl              
00005729  DL_Common_delayCycles                
00005761  SysTick_Handler                      
00005769  __aeabi_errno_addr                   
00005771  __aeabi_memcpy                       
00005771  __aeabi_memcpy4                      
00005771  __aeabi_memcpy8                      
00005779  abort                                
0000577e  C$$EXIT                              
0000577f  HOSTexit                             
00005783  Reset_Handler                        
00005799  _system_pre_init                     
000057a0  asc2_1608                            
00005d90  asc2_0806                            
00005fc0  __aeabi_ctype_table_                 
00005fc0  __aeabi_ctype_table_C                
000060c1  _IQ6div_lookup                       
00006254  __TI_Handler_Table_Base              
00006260  __TI_Handler_Table_Limit             
00006268  __TI_CINIT_Base                      
00006278  __TI_CINIT_Limit                     
00006278  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
20200400  Serial_RxData                        
2020071c  wit_dmaBuffer                        
2020073e  ret                                  
20200740  wit_data                             
20200760  ExISR_Flag                           
20200764  Motor_Font_Left                      
202007a4  Motor_Font_Right                     
202007e4  Data_Tracker_Input                   
202007ec  Motor                                
202007f4  Data_MotorEncoder                    
202007f8  Data_Motor_TarSpeed                  
202007fc  Data_Tracker_Offset                  
20200800  Data_wit_Offset                      
20200804  Data_wit_Target                      
20200808  Data_wit_UserTarget                  
20200810  __aeabi_errno                        
20200814  delayTick                            
2020081c  uwTick                               
20200822  Data_wit_ControlEnabled              
20200825  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[254 symbols]
