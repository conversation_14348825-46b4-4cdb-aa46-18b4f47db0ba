#include "Tracker.h"

uint8_t ret;

#define DIS_INRERVAL _IQ(1.5) //传感器间距

/**
 * @brief 读取循迹传感器并计算位置偏差
 * 
 * @param tck_ptr 8路传感器数据数组指针
 * @param offset_ptr 位置偏差值指针 (单位:cm, 负值表示偏左，正值表示偏右)
 * @return true 成功读取并计算偏差 
 * @return false 读取失败或参数错误
 */
bool Tracker_Read(uint8_t *tck_ptr, _iq *offset_ptr)
{
	uint8_t i;
    uint8_t test;
    
    if (tck_ptr == NULL || offset_ptr == NULL) return false;

    ret = 0;

	for (i = 0; i < 8; ++i) {
		/* 输出时钟下降沿 */
		DL_GPIO_clearPins(Serial_CLK_PORT, Serial_CLK_PIN);
		delay_us(2);
		ret |= (DL_GPIO_readPins(Serial_DAT_PORT, Serial_DAT_PIN)==0?0:1) << i;

		/* 输出时钟上升沿,让传感器更新数据*/
		DL_GPIO_setPins(Serial_CLK_PORT, Serial_CLK_PIN);
	
		/* 延迟需要在5us左右 */
		delay_us(5);
	}

    for (i = 0; i < 8; ++i) {
        tck_ptr[i]=(ret>>i)&0x01;
    }


    _iq pos_sum = _IQ(0);
    uint8_t cnt = 0;

    // 加权平均法计算位置
    for (uint8_t i = 0; i < 8; i++)
    {
        if (tck_ptr[i] == TRACK_ON)
        {
            // 传感器位置权重: 0,1,2,3,4,5,6,7 对应 -5.25,-3.75,-2.25,-0.75,0.75,2.25,3.75,5.25 cm
            _iq sensor_pos = _IQmpy(_IQ(i - 3.5f), DIS_INRERVAL);
            pos_sum += sensor_pos;
            cnt++;
        }
    }

    if (cnt == 0)
    {
        // 没有检测到线，保持上次偏差值
        return false;
    }

    // 计算加权平均位置偏差
    *offset_ptr = _IQdiv(pos_sum, _IQ(cnt));

    return true;

}

