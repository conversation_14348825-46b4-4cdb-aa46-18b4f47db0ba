#ifndef __PATH_CONFIG_H
#define __PATH_CONFIG_H

/**
 * @file PathConfig.h
 * @brief 智能小车路径角度控制配置文件
 * @version 1.0
 * @date 2025-01-28
 * <AUTHOR>
 * 
 * @description
 * 本配置文件用于解决第三阶段角度行驶方向错误问题
 * 基于路径图几何分析：A(左上) → C(右下) → B(右上) → D(左下) → A(左上)
 * 
 * 问题根源：
 * - case 2角度计算错误：B到D应该左转但设置为右转
 * - case 3逻辑缺失：只有延时没有角度控制
 * 
 * 解决方案：
 * - 修正TURN_ANGLE_B_TO_D为-40.0f（左转）
 * - 完善状态机逻辑和角度参数化管理
 */

// =============================================================================
// 路径转向角度配置
// =============================================================================

/**
 * @brief C到B段失线后的转向角度（左转）
 * @note 从右下角C点转向右上角B点，需要左转约90度
 */
#define TURN_ANGLE_C_TO_B    -40.0f

/**
 * @brief B到D段失线后的转向角度（左转）
 * @note 从右上角B点转向左下角D点，需要左转约135度
 * @warning 这是关键修正：原代码错误设置为+40.0f（右转）
 */
#define TURN_ANGLE_B_TO_D    -40.0f

/**
 * @brief D到A段失线后的转向角度（右转）
 * @note 从左下角D点转向左上角A点，需要右转约90度
 */
#define TURN_ANGLE_D_TO_A    +40.0f

// =============================================================================
// 路径状态枚举定义
// =============================================================================

/**
 * @brief 路径状态枚举
 * @description 明确定义各个路径段的状态，提高代码可读性
 */
typedef enum {
    PATH_A_TO_C = 0,            /**< A到C段正常循迹 */
    PATH_C_TO_B_TURNING = 1,    /**< C到B段转向中 */
    PATH_B_TO_D = 2,            /**< B到D段正常循迹 */
    PATH_D_TO_A_TURNING = 3,    /**< D到A段转向中（原case 3逻辑缺失位置） */
    PATH_COMPLETED = 4          /**< 任务完成 */
} PathState_t;

// =============================================================================
// 调试和控制配置
// =============================================================================

/**
 * @brief 启用路径调试信息
 * @note 设置为1启用OLED调试显示，设置为0关闭调试信息
 */
#define ENABLE_PATH_DEBUG    1

/**
 * @brief 角度控制容差
 * @note 角度控制的允许误差范围（度）
 */
#define ANGLE_TOLERANCE      2.0f

/**
 * @brief 失线检测延时阈值
 * @note 失线多少个周期后开始角度控制（单位：10ms）
 */
#define LOST_TIME_THRESHOLD  20

/**
 * @brief 找线稳定检测阈值
 * @note 检测到线多少个周期后恢复循迹（单位：10ms）
 */
#define FOUND_TIME_THRESHOLD 10

// =============================================================================
// 角度范围处理宏
// =============================================================================

/**
 * @brief 角度标准化宏
 * @param angle 需要标准化的角度值
 * @description 将角度值标准化到[-180, 180]范围内
 */
#define NORMALIZE_ANGLE(angle) do { \
    while ((angle) > 180.0f) (angle) -= 360.0f; \
    while ((angle) < -180.0f) (angle) += 360.0f; \
} while(0)

// =============================================================================
// 使用说明
// =============================================================================

/**
 * @brief 配置文件使用说明
 * 
 * 1. 在Task_App.c中包含此头文件：#include "PathConfig.h"
 * 
 * 2. 使用角度宏替代硬编码值：
 *    原代码：target_angle = wit_data.yaw + 40.0f;
 *    优化后：target_angle = wit_data.yaw + TURN_ANGLE_B_TO_D;
 * 
 * 3. 使用状态枚举替代数字常量：
 *    原代码：current_path = 3;
 *    优化后：current_path = PATH_D_TO_A_TURNING;
 * 
 * 4. 启用调试信息：
 *    #if ENABLE_PATH_DEBUG
 *        OLED_Printf(0, 16*0, 8, "target:%.1f", target_angle);
 *    #endif
 * 
 * 5. 角度标准化处理：
 *    NORMALIZE_ANGLE(target_angle);
 */

#endif /* __PATH_CONFIG_H */
