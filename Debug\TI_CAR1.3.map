******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 29 21:58:17 2025

OUTPUT FILE NAME:   <TI_CAR1.3.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004e49


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000064a0  00019b60  R  X
  SRAM                  20200000   00008000  00000bb2  0000744e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000064a0   000064a0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000058e0   000058e0    r-x .text
  000059a0    000059a0    00000aa0   00000aa0    r-- .rodata
  00006440    00006440    00000060   00000060    r-- .cinit
20200000    20200000    000009b2   00000000    rw-
  20200000    20200000    000004f0   00000000    rw- .bss
  202004f0    202004f0    00000400   00000000    rw- .sysmem
  202008f0    202008f0    000000c2   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000058e0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000368     Interrupt.o (.text.UART0_IRQHandler)
                  00000df8    000002e0     Task_App.o (.text.Task_AutoRecover)
                  000010d8    00000254     wit.o (.text.wit_direct)
                  0000132c    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000154c    0000020c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001758    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001934    000001b0     Task.o (.text.Task_Start)
                  00001ae4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001c76    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001c78    0000015c     Tracker.o (.text.Tracker_Read)
                  00001dd4    00000144     PID.o (.text.PID_SProsc)
                  00001f18    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002054    00000134            : qsort.c.obj (.text.qsort)
                  00002188    00000130     OLED.o (.text.OLED_ShowChar)
                  000022b8    0000012c     PID.o (.text.PID_AProsc)
                  000023e4    00000120     Task_App.o (.text.Task_Motor_PID)
                  00002504    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002624    00000110     OLED.o (.text.OLED_Init)
                  00002734    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00002840    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  0000294c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002a50    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002b34    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002c10    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002ce8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002dc0    000000d0     Task_App.o (.text.Task_Init)
                  00002e90    000000c4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002f54    000000b4     Task.o (.text.Task_Add)
                  00003008    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  000030b8    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000315a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000315c    0000009c     Motor.o (.text.Motor_SetDuty)
                  000031f8    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00003290    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00003320    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  000033ac    0000008c     Task_App.o (.text.Task_wit)
                  00003438    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000034c4    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003548    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_K230_init)
                  000035c8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003644    00000074     Motor.o (.text.Motor_SetDirc)
                  000036b8    00000074     Motor.o (.text.Motor_Start)
                  0000372c    00000074     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  000037a0    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003814    0000000c     SysTick.o (.text.Sys_GetTick)
                  00003820    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003894    00000074     SysTick.o (.text.delay_us)
                  00003908    0000006e     OLED.o (.text.OLED_ShowString)
                  00003976    00000002     --HOLE-- [fill = 0]
                  00003978    0000006c     Task_App.o (.text.Task_OLED)
                  000039e4    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00003a4e    00000002     --HOLE-- [fill = 0]
                  00003a50    00000068     Task_App.o (.text.Task_Key)
                  00003ab8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003b20    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003b86    00000002     --HOLE-- [fill = 0]
                  00003b88    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00003bec    00000064     Key_Led.o (.text.Key_Read)
                  00003c50    00000064     wit.o (.text.WIT_Init)
                  00003cb4    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003d16    00000002     --HOLE-- [fill = 0]
                  00003d18    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003d7a    00000002     --HOLE-- [fill = 0]
                  00003d7c    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003ddc    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003e3c    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003e9a    00000002     --HOLE-- [fill = 0]
                  00003e9c    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003ef8    0000005c     Task_App.o (.text.Task_Tracker)
                  00003f54    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003fb0    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  0000400c    00000058     Serial.o (.text.Serial_Init)
                  00004064    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000040bc    00000058            : _printfi.c.obj (.text._pconv_f)
                  00004114    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000416a    00000002     --HOLE-- [fill = 0]
                  0000416c    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  000041c0    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004214    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00004266    00000002     --HOLE-- [fill = 0]
                  00004268    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000042b8    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00004308    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004354    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000043a0    0000004c     OLED.o (.text.OLED_Printf)
                  000043ec    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00004436    00000002     --HOLE-- [fill = 0]
                  00004438    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004480    00000048     Motor.o (.text.Motor_GetSpeed)
                  000044c8    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00004510    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004554    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00004596    00000002     --HOLE-- [fill = 0]
                  00004598    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_bujingA_init)
                  000045d8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_bujingB_init)
                  00004618    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004658    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004698    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000046d8    0000003e     Task.o (.text.Task_CMP)
                  00004716    00000002     --HOLE-- [fill = 0]
                  00004718    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004754    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004790    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000047cc    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00004808    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004844    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004880    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000048bc    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000048f6    00000002     --HOLE-- [fill = 0]
                  000048f8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004932    00000002     --HOLE-- [fill = 0]
                  00004934    00000038     Interrupt.o (.text.Interrupt_Init)
                  0000496c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000049a4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000049d8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004a0c    00000034     Task_App.o (.text.Task_IdleFunction)
                  00004a40    00000030     Interrupt.o (.text.DL_DMA_setTransferSize)
                  00004a70    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00004aa0    00000030     wit.o (.text.DL_DMA_setTransferSize)
                  00004ad0    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00004b00    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00004b30    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004b60    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00004b8c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00004bb8    0000002c     wit.o (.text.__NVIC_EnableIRQ)
                  00004be4    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004c10    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00004c3c    0000002a     PID.o (.text.PID_Init)
                  00004c66    00000028     OLED.o (.text.DL_Common_updateReg)
                  00004c8e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004cb6    00000002     --HOLE-- [fill = 0]
                  00004cb8    00000028     Interrupt.o (.text.DL_DMA_setDestAddr)
                  00004ce0    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00004d08    00000028     wit.o (.text.DL_DMA_setDestAddr)
                  00004d30    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00004d58    00000028     wit.o (.text.DL_DMA_setSrcAddr)
                  00004d80    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004da8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004dd0    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004df8    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00004e20    00000028     SysTick.o (.text.SysTick_Increasment)
                  00004e48    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004e70    00000026     Interrupt.o (.text.DL_DMA_disableChannel)
                  00004e96    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00004ebc    00000026     Interrupt.o (.text.DL_DMA_enableChannel)
                  00004ee2    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00004f08    00000026     wit.o (.text.DL_DMA_enableChannel)
                  00004f2e    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004f54    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004f7a    00000002     --HOLE-- [fill = 0]
                  00004f7c    00000024     Interrupt.o (.text.DL_DMA_getTransferSize)
                  00004fa0    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004fc4    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00004fe8    00000022     PID.o (.text.PID_SetParams)
                  0000500a    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  0000502c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0000504c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  0000506c    00000020     SysTick.o (.text.Delay)
                  0000508c    00000020     main.o (.text.main)
                  000050ac    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000050ca    00000002     --HOLE-- [fill = 0]
                  000050cc    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000050ea    00000002     --HOLE-- [fill = 0]
                  000050ec    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00005108    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00005124    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00005140    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  0000515c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00005178    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00005194    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000051b0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000051cc    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000051e8    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00005204    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00005220    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000523c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005258    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005274    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00005290    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000052ac    0000001c     wit.o (.text.WIT_SetTargetAngle)
                  000052c8    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000052e0    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000052f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005310    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005328    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00005340    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00005358    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005370    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00005388    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000053a0    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000053b8    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000053d0    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  000053e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005400    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005418    00000018     Task_App.o (.text.DL_GPIO_togglePins)
                  00005430    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00005448    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005460    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005478    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005490    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000054a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000054c0    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000054d8    00000018     OLED.o (.text.DL_I2C_reset)
                  000054f0    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005508    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005520    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00005538    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00005550    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005568    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005580    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00005598    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000055b0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000055c8    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  000055e0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000055f8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00005610    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005628    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005640    00000018     Interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  00005658    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005670    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00005688    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  000056a0    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  000056b8    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000056ce    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000056e4    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000056fa    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00005710    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005726    00000016     wit.o (.text.__f64_bits_as_u64)
                  0000573c    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00005750    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00005764    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  00005778    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000578c    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000057a0    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000057b4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000057c8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000057dc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000057f0    00000014     Interrupt.o (.text.DL_UART_receiveData)
                  00005804    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005818    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000582c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000583e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005850    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005862    00000002     --HOLE-- [fill = 0]
                  00005864    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005874    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005884    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00005894    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000058a4    00000010     wit.o (.text.WIT_IsTargetControlActive)
                  000058b4    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000058c4    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  000058d4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000058e2    0000000e     wit.o (.text.__f32_bits_as_u32)
                  000058f0    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000058fe    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000590c    0000000c     wit.o (.text.WIT_CancelTargetControl)
                  00005918    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00005924    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000592e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005938    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005948    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005952    0000000a            : vsprintf.c.obj (.text._outc)
                  0000595c    00000008     Interrupt.o (.text.SysTick_Handler)
                  00005964    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000596c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00005974    00000006     libc.a : exit.c.obj (.text:abort)
                  0000597a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000597e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00005982    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005986    00000002     --HOLE-- [fill = 0]
                  00005988    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005998    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000599c    00000004     --HOLE-- [fill = 0]

.cinit     0    00006440    00000060     
                  00006440    00000035     (.cinit..data.load) [load image, compression = lzss]
                  00006475    00000003     --HOLE-- [fill = 0]
                  00006478    0000000c     (__TI_handler_table)
                  00006484    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000648c    00000010     (__TI_cinit_table)
                  0000649c    00000004     --HOLE-- [fill = 0]

.rodata    0    000059a0    00000aa0     
                  000059a0    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00005f90    00000228     OLED_Font.o (.rodata.asc2_0806)
                  000061b8    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  000061c0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000062c1    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00006302    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00006304    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000632c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00006344    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000635c    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00006374    00000013     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00006387    00000012     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00006399    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000063aa    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000063bb    00000010     Task_App.o (.rodata.str1.14074990341397557290.1)
                  000063cb    0000000c     Task_App.o (.rodata.str1.11683036942922059812.1)
                  000063d7    0000000b     Task_App.o (.rodata.str1.492715258893803702.1)
                  000063e2    0000000a     ti_msp_dl_config.o (.rodata.gUART_K230Config)
                  000063ec    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  000063f6    0000000a     ti_msp_dl_config.o (.rodata.gUART_bujingAConfig)
                  00006400    0000000a     ti_msp_dl_config.o (.rodata.gUART_bujingBConfig)
                  0000640a    00000002     ti_msp_dl_config.o (.rodata.gUART_K230ClockConfig)
                  0000640c    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00006414    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000641c    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00006422    00000005     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00006427    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000642b    00000004     Task_App.o (.rodata.str1.8896853068034818020.1)
                  0000642f    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00006432    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00006435    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00006437    00000002     ti_msp_dl_config.o (.rodata.gUART_bujingAClockConfig)
                  00006439    00000002     ti_msp_dl_config.o (.rodata.gUART_bujingBClockConfig)
                  0000643b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000004f0     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorBFrontBackup)
                  202003ac    000000a0     (.common:gMotorAFrontBackup)
                  2020044c    00000030     (.common:gUART_bujingBBackup)
                  2020047c    0000002c     wit.o (.bss.Angle_PID_Instance)
                  202004a8    00000021     (.common:wit_dmaBuffer)
                  202004c9    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  202004ca    00000001     (.common:ret)
                  202004cb    00000001     --HOLE--
                  202004cc    00000020     (.common:wit_data)
                  202004ec    00000004     (.common:ExISR_Flag)

.sysmem    0    202004f0    00000400     UNINITIALIZED
                  202004f0    00000010     libc.a : memory.c.obj (.sysmem)
                  20200500    000003f0     --HOLE--

.data      0    202008f0    000000c2     UNINITIALIZED
                  202008f0    00000040     Motor.o (.data.Motor_Font_Left)
                  20200930    00000040     Motor.o (.data.Motor_Font_Right)
                  20200970    00000008     Task_App.o (.data.Data_Tracker_Input)
                  20200978    00000008     Task_App.o (.data.Motor)
                  20200980    00000004     Task_App.o (.data.Data_MotorEncoder)
                  20200984    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  20200988    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  2020098c    00000004     Task_App.o (.data.Data_wit_Offset)
                  20200990    00000004     Task_App.o (.data.Data_wit_Target)
                  20200994    00000004     Task_App.o (.data.Data_wit_UserTarget)
                  20200998    00000004     Task_App.o (.data.Task_AutoRecover.line_found_time)
                  2020099c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202009a0    00000004     SysTick.o (.data.delayTick)
                  202009a4    00000004     Task_App.o (.data.lost_time)
                  202009a8    00000004     SysTick.o (.data.uwTick)
                  202009ac    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202009ae    00000001     Task_App.o (.data.Data_wit_ControlEnabled)
                  202009af    00000001     Task.o (.data.Task_Num)
                  202009b0    00000001     Task_App.o (.data.current_path)
                  202009b1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3598    184       396    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3638    376       396    
                                                               
    .\APP\Src\
       Task_App.o                     1752    103       53     
       Interrupt.o                    1526    0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         3278    103       58     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       wit.o                          998     0         109    
       Task.o                         674     0         241    
       Serial.o                       292     0         512    
       PID.o                          700     0         0      
       Motor.o                        528     0         128    
       Tracker.o                      414     0         1      
       SysTick.o                      200     0         8      
       Key_Led.o                      122     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5786    2072      999    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1136    0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5730    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2778    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       89        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   22710   2996      2993   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000648c records: 2, size/record: 8, table size: 16
	.data: load addr=00006440, load size=00000035 bytes, run addr=202008f0, run size=000000c2 bytes, compression=lzss
	.bss: load addr=00006484, load size=00000008 bytes, run addr=20200000, run size=000004f0 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006478 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001ae5     00005938     00005936   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004e49     00005988     00005982   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000597b  ADC0_IRQHandler                      
0000597b  ADC1_IRQHandler                      
0000597b  AES_IRQHandler                       
0000597e  C$$EXIT                              
0000597b  CANFD0_IRQHandler                    
0000597b  DAC0_IRQHandler                      
00005925  DL_Common_delayCycles                
00004309  DL_DMA_initChannel                   
00003e3d  DL_I2C_fillControllerTXFIFO          
00004f55  DL_I2C_setClockConfig                
00002b35  DL_SYSCTL_configSYSPLL               
00003b89  DL_SYSCTL_setHFCLKSourceHFXTParams   
00004511  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000294d  DL_Timer_initFourCCPWMMode           
00005259  DL_Timer_setCaptCompUpdateMethod     
00005599  DL_Timer_setCaptureCompareOutCtl     
00005875  DL_Timer_setCaptureCompareValue      
00005275  DL_Timer_setClockConfig              
0000416d  DL_UART_drainRXFIFO                  
00004439  DL_UART_init                         
0000582d  DL_UART_setClockConfig               
0000597b  DMA_IRQHandler                       
20200980  Data_MotorEncoder                    
20200984  Data_Motor_TarSpeed                  
20200970  Data_Tracker_Input                   
20200988  Data_Tracker_Offset                  
202009ae  Data_wit_ControlEnabled              
2020098c  Data_wit_Offset                      
20200990  Data_wit_Target                      
20200994  Data_wit_UserTarget                  
0000597b  Default_Handler                      
0000506d  Delay                                
202004ec  ExISR_Flag                           
0000597b  GROUP0_IRQHandler                    
00003009  GROUP1_IRQHandler                    
0000597f  HOSTexit                             
0000597b  HardFault_Handler                    
0000597b  I2C0_IRQHandler                      
0000597b  I2C1_IRQHandler                      
000039e5  I2C_OLED_Clear                       
000047cd  I2C_OLED_Set_Pos                     
000031f9  I2C_OLED_WR_Byte                     
00003d7d  I2C_OLED_i2c_sda_unlock              
00004935  Interrupt_Init                       
00003bed  Key_Read                             
20200978  Motor                                
202008f0  Motor_Font_Left                      
20200930  Motor_Font_Right                     
00004481  Motor_GetSpeed                       
0000315d  Motor_SetDuty                        
000036b9  Motor_Start                          
0000597b  NMI_Handler                          
00002625  OLED_Init                            
000043a1  OLED_Printf                          
00002189  OLED_ShowChar                        
00003909  OLED_ShowString                      
000022b9  PID_AProsc                           
00004c3d  PID_Init                             
00001dd5  PID_SProsc                           
00004fe9  PID_SetParams                        
0000597b  PendSV_Handler                       
0000597b  RTC_IRQHandler                       
00005983  Reset_Handler                        
0000597b  SPI0_IRQHandler                      
0000597b  SPI1_IRQHandler                      
0000597b  SVC_Handler                          
00004ad1  SYSCFG_DL_DMA_CH_RX_init             
00005671  SYSCFG_DL_DMA_CH_TX_init             
00005689  SYSCFG_DL_DMA_WIT_init               
00005885  SYSCFG_DL_DMA_init                   
0000154d  SYSCFG_DL_GPIO_init                  
00003ddd  SYSCFG_DL_I2C_OLED_init              
00003321  SYSCFG_DL_MotorAFront_init           
00003291  SYSCFG_DL_MotorBFront_init           
00003e9d  SYSCFG_DL_SYSCTL_init                
00005895  SYSCFG_DL_SYSTICK_init               
00003549  SYSCFG_DL_UART_K230_init             
0000372d  SYSCFG_DL_UART_WIT_init              
00004599  SYSCFG_DL_UART_bujingA_init          
000045d9  SYSCFG_DL_UART_bujingB_init          
000041c1  SYSCFG_DL_init                       
00002e91  SYSCFG_DL_initPower                  
0000400d  Serial_Init                          
20200000  Serial_RxData                        
0000595d  SysTick_Handler                      
00004e21  SysTick_Increasment                  
00003815  Sys_GetTick                          
0000597b  TIMA0_IRQHandler                     
0000597b  TIMA1_IRQHandler                     
0000597b  TIMG0_IRQHandler                     
0000597b  TIMG12_IRQHandler                    
0000597b  TIMG6_IRQHandler                     
0000597b  TIMG7_IRQHandler                     
0000597b  TIMG8_IRQHandler                     
0000583f  TI_memcpy_small                      
000058ff  TI_memset_small                      
00002f55  Task_Add                             
00000df9  Task_AutoRecover                     
00004a0d  Task_IdleFunction                    
00002dc1  Task_Init                            
00003a51  Task_Key                             
000023e5  Task_Motor_PID                       
00003979  Task_OLED                            
00001935  Task_Start                           
00003ef9  Task_Tracker                         
000033ad  Task_wit                             
00001c79  Tracker_Read                         
00000a91  UART0_IRQHandler                     
0000597b  UART1_IRQHandler                     
0000597b  UART2_IRQHandler                     
0000597b  UART3_IRQHandler                     
0000590d  WIT_CancelTargetControl              
00003c51  WIT_Init                             
000058a5  WIT_IsTargetControlActive            
000052ad  WIT_SetTargetAngle                   
00002735  _IQ24div                             
00004b61  _IQ24mpy                             
00004b01  _IQ24toF                             
000062c1  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000648c  __TI_CINIT_Base                      
0000649c  __TI_CINIT_Limit                     
0000649c  __TI_CINIT_Warm                      
00006478  __TI_Handler_Table_Base              
00006484  __TI_Handler_Table_Limit             
00004881  __TI_auto_init_nobinit_nopinit       
000035c9  __TI_decompress_lzss                 
00005851  __TI_decompress_none                 
00004065  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000058c5  __TI_zero_init                       
00001aef  __adddf3                             
00002cf3  __addsf3                             
000061c0  __aeabi_ctype_table_                 
000061c0  __aeabi_ctype_table_C                
00003821  __aeabi_d2f                          
000043ed  __aeabi_d2iz                         
00004555  __aeabi_d2uiz                        
00001aef  __aeabi_dadd                         
00003cb5  __aeabi_dcmpeq                       
00003cf1  __aeabi_dcmpge                       
00003d05  __aeabi_dcmpgt                       
00003cdd  __aeabi_dcmple                       
00003cc9  __aeabi_dcmplt                       
00002841  __aeabi_ddiv                         
00002a51  __aeabi_dmul                         
00001ae5  __aeabi_dsub                         
2020099c  __aeabi_errno                        
00005965  __aeabi_errno_addr                   
00004659  __aeabi_f2d                          
0000496d  __aeabi_f2iz                         
00002cf3  __aeabi_fadd                         
00003d19  __aeabi_fcmpeq                       
00003d55  __aeabi_fcmpge                       
00003d69  __aeabi_fcmpgt                       
00003d41  __aeabi_fcmple                       
00003d2d  __aeabi_fcmplt                       
00003439  __aeabi_fmul                         
00002ce9  __aeabi_fsub                         
00004be5  __aeabi_i2d                          
00004809  __aeabi_i2f                          
00004115  __aeabi_idiv                         
00001c77  __aeabi_idiv0                        
00004115  __aeabi_idivmod                      
0000315b  __aeabi_ldiv0                        
000050cd  __aeabi_llsl                         
00004fc5  __aeabi_lmul                         
00005919  __aeabi_memclr                       
00005919  __aeabi_memclr4                      
00005919  __aeabi_memclr8                      
0000596d  __aeabi_memcpy                       
0000596d  __aeabi_memcpy4                      
0000596d  __aeabi_memcpy8                      
000058d5  __aeabi_memset                       
000058d5  __aeabi_memset4                      
000058d5  __aeabi_memset8                      
00004619  __aeabi_uidiv                        
00004619  __aeabi_uidivmod                     
00005805  __aeabi_uldivmod                     
000050cd  __ashldi3                            
ffffffff  __binit__                            
00003ab9  __cmpdf2                             
000048bd  __cmpsf2                             
00002841  __divdf3                             
00003ab9  __eqdf2                              
000048bd  __eqsf2                              
00004659  __extendsfdf2                        
000043ed  __fixdfsi                            
0000496d  __fixsfsi                            
00004555  __fixunsdfsi                         
00004be5  __floatsidf                          
00004809  __floatsisf                          
000037a1  __gedf2                              
00004845  __gesf2                              
000037a1  __gtdf2                              
00004845  __gtsf2                              
00003ab9  __ledf2                              
000048bd  __lesf2                              
00003ab9  __ltdf2                              
000048bd  __ltsf2                              
UNDEFED   __mpu_init                           
00002a51  __muldf3                             
00004fc5  __muldi3                             
000048f9  __muldsi3                            
00003439  __mulsf3                             
00003ab9  __nedf2                              
000048bd  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001ae5  __subdf3                             
00002ce9  __subsf3                             
00003821  __truncdfsf2                         
000030b9  __udivmoddi4                         
00004e49  _c_int00_noargs                      
202004f0  _sys_memory                          
UNDEFED   _system_post_cinit                   
00005999  _system_pre_init                     
00005975  abort                                
00005f90  asc2_0806                            
000059a0  asc2_1608                            
00004699  atoi                                 
ffffffff  binit                                
202009a0  delayTick                            
00003895  delay_us                             
202009b1  enable_group1_irq                    
00003f55  frexp                                
00003f55  frexpl                               
202003ac  gMotorAFrontBackup                   
202002f0  gMotorBFrontBackup                   
2020044c  gUART_bujingBBackup                  
00000000  interruptVectors                     
00002c11  ldexp                                
00002c11  ldexpl                               
0000508d  main                                 
0000500b  memccpy                              
00002055  qsort                                
202004ca  ret                                  
00002c11  scalbn                               
00002c11  scalbnl                              
202009a8  uwTick                               
00004c11  vsprintf                             
000058b5  wcslen                               
202004cc  wit_data                             
000010d9  wit_direct                           
202004a8  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000a91  UART0_IRQHandler                     
00000df9  Task_AutoRecover                     
000010d9  wit_direct                           
0000154d  SYSCFG_DL_GPIO_init                  
00001935  Task_Start                           
00001ae5  __aeabi_dsub                         
00001ae5  __subdf3                             
00001aef  __adddf3                             
00001aef  __aeabi_dadd                         
00001c77  __aeabi_idiv0                        
00001c79  Tracker_Read                         
00001dd5  PID_SProsc                           
00002055  qsort                                
00002189  OLED_ShowChar                        
000022b9  PID_AProsc                           
000023e5  Task_Motor_PID                       
00002625  OLED_Init                            
00002735  _IQ24div                             
00002841  __aeabi_ddiv                         
00002841  __divdf3                             
0000294d  DL_Timer_initFourCCPWMMode           
00002a51  __aeabi_dmul                         
00002a51  __muldf3                             
00002b35  DL_SYSCTL_configSYSPLL               
00002c11  ldexp                                
00002c11  ldexpl                               
00002c11  scalbn                               
00002c11  scalbnl                              
00002ce9  __aeabi_fsub                         
00002ce9  __subsf3                             
00002cf3  __addsf3                             
00002cf3  __aeabi_fadd                         
00002dc1  Task_Init                            
00002e91  SYSCFG_DL_initPower                  
00002f55  Task_Add                             
00003009  GROUP1_IRQHandler                    
000030b9  __udivmoddi4                         
0000315b  __aeabi_ldiv0                        
0000315d  Motor_SetDuty                        
000031f9  I2C_OLED_WR_Byte                     
00003291  SYSCFG_DL_MotorBFront_init           
00003321  SYSCFG_DL_MotorAFront_init           
000033ad  Task_wit                             
00003439  __aeabi_fmul                         
00003439  __mulsf3                             
00003549  SYSCFG_DL_UART_K230_init             
000035c9  __TI_decompress_lzss                 
000036b9  Motor_Start                          
0000372d  SYSCFG_DL_UART_WIT_init              
000037a1  __gedf2                              
000037a1  __gtdf2                              
00003815  Sys_GetTick                          
00003821  __aeabi_d2f                          
00003821  __truncdfsf2                         
00003895  delay_us                             
00003909  OLED_ShowString                      
00003979  Task_OLED                            
000039e5  I2C_OLED_Clear                       
00003a51  Task_Key                             
00003ab9  __cmpdf2                             
00003ab9  __eqdf2                              
00003ab9  __ledf2                              
00003ab9  __ltdf2                              
00003ab9  __nedf2                              
00003b89  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003bed  Key_Read                             
00003c51  WIT_Init                             
00003cb5  __aeabi_dcmpeq                       
00003cc9  __aeabi_dcmplt                       
00003cdd  __aeabi_dcmple                       
00003cf1  __aeabi_dcmpge                       
00003d05  __aeabi_dcmpgt                       
00003d19  __aeabi_fcmpeq                       
00003d2d  __aeabi_fcmplt                       
00003d41  __aeabi_fcmple                       
00003d55  __aeabi_fcmpge                       
00003d69  __aeabi_fcmpgt                       
00003d7d  I2C_OLED_i2c_sda_unlock              
00003ddd  SYSCFG_DL_I2C_OLED_init              
00003e3d  DL_I2C_fillControllerTXFIFO          
00003e9d  SYSCFG_DL_SYSCTL_init                
00003ef9  Task_Tracker                         
00003f55  frexp                                
00003f55  frexpl                               
0000400d  Serial_Init                          
00004065  __TI_ltoa                            
00004115  __aeabi_idiv                         
00004115  __aeabi_idivmod                      
0000416d  DL_UART_drainRXFIFO                  
000041c1  SYSCFG_DL_init                       
00004309  DL_DMA_initChannel                   
000043a1  OLED_Printf                          
000043ed  __aeabi_d2iz                         
000043ed  __fixdfsi                            
00004439  DL_UART_init                         
00004481  Motor_GetSpeed                       
00004511  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004555  __aeabi_d2uiz                        
00004555  __fixunsdfsi                         
00004599  SYSCFG_DL_UART_bujingA_init          
000045d9  SYSCFG_DL_UART_bujingB_init          
00004619  __aeabi_uidiv                        
00004619  __aeabi_uidivmod                     
00004659  __aeabi_f2d                          
00004659  __extendsfdf2                        
00004699  atoi                                 
000047cd  I2C_OLED_Set_Pos                     
00004809  __aeabi_i2f                          
00004809  __floatsisf                          
00004845  __gesf2                              
00004845  __gtsf2                              
00004881  __TI_auto_init_nobinit_nopinit       
000048bd  __cmpsf2                             
000048bd  __eqsf2                              
000048bd  __lesf2                              
000048bd  __ltsf2                              
000048bd  __nesf2                              
000048f9  __muldsi3                            
00004935  Interrupt_Init                       
0000496d  __aeabi_f2iz                         
0000496d  __fixsfsi                            
00004a0d  Task_IdleFunction                    
00004ad1  SYSCFG_DL_DMA_CH_RX_init             
00004b01  _IQ24toF                             
00004b61  _IQ24mpy                             
00004be5  __aeabi_i2d                          
00004be5  __floatsidf                          
00004c11  vsprintf                             
00004c3d  PID_Init                             
00004e21  SysTick_Increasment                  
00004e49  _c_int00_noargs                      
00004f55  DL_I2C_setClockConfig                
00004fc5  __aeabi_lmul                         
00004fc5  __muldi3                             
00004fe9  PID_SetParams                        
0000500b  memccpy                              
0000506d  Delay                                
0000508d  main                                 
000050cd  __aeabi_llsl                         
000050cd  __ashldi3                            
00005259  DL_Timer_setCaptCompUpdateMethod     
00005275  DL_Timer_setClockConfig              
000052ad  WIT_SetTargetAngle                   
00005599  DL_Timer_setCaptureCompareOutCtl     
00005671  SYSCFG_DL_DMA_CH_TX_init             
00005689  SYSCFG_DL_DMA_WIT_init               
00005805  __aeabi_uldivmod                     
0000582d  DL_UART_setClockConfig               
0000583f  TI_memcpy_small                      
00005851  __TI_decompress_none                 
00005875  DL_Timer_setCaptureCompareValue      
00005885  SYSCFG_DL_DMA_init                   
00005895  SYSCFG_DL_SYSTICK_init               
000058a5  WIT_IsTargetControlActive            
000058b5  wcslen                               
000058c5  __TI_zero_init                       
000058d5  __aeabi_memset                       
000058d5  __aeabi_memset4                      
000058d5  __aeabi_memset8                      
000058ff  TI_memset_small                      
0000590d  WIT_CancelTargetControl              
00005919  __aeabi_memclr                       
00005919  __aeabi_memclr4                      
00005919  __aeabi_memclr8                      
00005925  DL_Common_delayCycles                
0000595d  SysTick_Handler                      
00005965  __aeabi_errno_addr                   
0000596d  __aeabi_memcpy                       
0000596d  __aeabi_memcpy4                      
0000596d  __aeabi_memcpy8                      
00005975  abort                                
0000597b  ADC0_IRQHandler                      
0000597b  ADC1_IRQHandler                      
0000597b  AES_IRQHandler                       
0000597b  CANFD0_IRQHandler                    
0000597b  DAC0_IRQHandler                      
0000597b  DMA_IRQHandler                       
0000597b  Default_Handler                      
0000597b  GROUP0_IRQHandler                    
0000597b  HardFault_Handler                    
0000597b  I2C0_IRQHandler                      
0000597b  I2C1_IRQHandler                      
0000597b  NMI_Handler                          
0000597b  PendSV_Handler                       
0000597b  RTC_IRQHandler                       
0000597b  SPI0_IRQHandler                      
0000597b  SPI1_IRQHandler                      
0000597b  SVC_Handler                          
0000597b  TIMA0_IRQHandler                     
0000597b  TIMA1_IRQHandler                     
0000597b  TIMG0_IRQHandler                     
0000597b  TIMG12_IRQHandler                    
0000597b  TIMG6_IRQHandler                     
0000597b  TIMG7_IRQHandler                     
0000597b  TIMG8_IRQHandler                     
0000597b  UART1_IRQHandler                     
0000597b  UART2_IRQHandler                     
0000597b  UART3_IRQHandler                     
0000597e  C$$EXIT                              
0000597f  HOSTexit                             
00005983  Reset_Handler                        
00005999  _system_pre_init                     
000059a0  asc2_1608                            
00005f90  asc2_0806                            
000061c0  __aeabi_ctype_table_                 
000061c0  __aeabi_ctype_table_C                
000062c1  _IQ6div_lookup                       
00006478  __TI_Handler_Table_Base              
00006484  __TI_Handler_Table_Limit             
0000648c  __TI_CINIT_Base                      
0000649c  __TI_CINIT_Limit                     
0000649c  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorBFrontBackup                   
202003ac  gMotorAFrontBackup                   
2020044c  gUART_bujingBBackup                  
202004a8  wit_dmaBuffer                        
202004ca  ret                                  
202004cc  wit_data                             
202004ec  ExISR_Flag                           
202004f0  _sys_memory                          
202008f0  Motor_Font_Left                      
20200930  Motor_Font_Right                     
20200970  Data_Tracker_Input                   
20200978  Motor                                
20200980  Data_MotorEncoder                    
20200984  Data_Motor_TarSpeed                  
20200988  Data_Tracker_Offset                  
2020098c  Data_wit_Offset                      
20200990  Data_wit_Target                      
20200994  Data_wit_UserTarget                  
2020099c  __aeabi_errno                        
202009a0  delayTick                            
202009a8  uwTick                               
202009ae  Data_wit_ControlEnabled              
202009b1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[263 symbols]
