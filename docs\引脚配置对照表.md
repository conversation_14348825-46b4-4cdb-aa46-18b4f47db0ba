# TI_CAR1.3 引脚配置对照表

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-29
- **更新日期**: 2025-07-29
- **负责人**: <PERSON> (Engineer)

## 概述
本文档记录了TI_CAR1.3工程中所有引脚的配置情况，确保代码中的引脚使用与empty.syscfg配置文件生成的ti_msp_dl_config.h定义保持一致。

## 引脚配置验证结果

### ✅ 已验证正确的引脚配置

#### 1. 编码器引脚 (SPD_READER)
**配置文件定义**:
- SPD_READER_A_PORT = GPIOB
- SPD_READER_A_FONT_LEFT_A_PIN = DL_GPIO_PIN_11 (PB11)
- SPD_READER_A_FONT_RIGHT_A_PIN = DL_GPIO_PIN_9 (PB9)
- SPD_READER_B_PORT = GPIOB
- SPD_READER_B_FONT_LEFT_B_PIN = DL_GPIO_PIN_10 (PB10)
- SPD_READER_B_FONT_RIGHT_B_PIN = DL_GPIO_PIN_8 (PB8)

**代码使用情况**:
- ✅ APP/Src/Interrupt.c: 使用SPD_READER_A_PORT和相关宏定义
- ✅ APP/Inc/Interrupt.h: 定义了统一的宏定义

#### 2. 电机PWM定时器
**配置文件定义**:
- MotorAFront_INST = TIMG7 (左前轮)
- MotorBFront_INST = TIMA1 (右前轮)

**代码使用情况**:
- ✅ BSP/Src/Motor.c: 正确使用MotorAFront_INST和MotorBFront_INST

#### 3. 电机方向控制引脚 (DIRC_CTRL)
**配置文件定义**:
- DIRC_CTRL_PORT = GPIOA
- DIRC_CTRL_FONT_LEFT_PIN = DL_GPIO_PIN_23 (PA23)
- DIRC_CTRL_FONT_RIGHT_PIN = DL_GPIO_PIN_22 (PA22)

**代码使用情况**:
- ✅ BSP/Src/Motor.c: 正确使用DIRC_CTRL_PORT和相关引脚宏定义

#### 4. 按键引脚 (KEY)
**配置文件定义**:
- KEY_KEY2_PORT, KEY_KEY2_PIN
- KEY_KEY3_PORT, KEY_KEY3_PIN  
- KEY_KEY4_PORT, KEY_KEY4_PIN

**代码使用情况**:
- ✅ BSP/Src/Key_Led.c: 正确使用syscfg生成的宏定义

#### 5. 串行通信引脚 (Serial)
**配置文件定义**:
- Serial_CLK_PORT = GPIOB
- Serial_CLK_PIN = DL_GPIO_PIN_20 (PB20)
- Serial_DAT_PORT = GPIOA
- Serial_DAT_PIN = DL_GPIO_PIN_24 (PA24)

**代码使用情况**:
- ✅ BSP/Src/Tracker.c: 已修正，使用Serial_CLK_PORT和Serial_DAT_PORT

#### 6. I2C OLED引脚
**配置文件定义**:
- I2C_OLED_INST = I2C0
- GPIO_I2C_OLED_SCL_PORT, GPIO_I2C_OLED_SCL_PIN
- GPIO_I2C_OLED_SDA_PORT, GPIO_I2C_OLED_SDA_PIN

**代码使用情况**:
- ✅ BSP/Src/OLED.c: 正确使用syscfg生成的宏定义

#### 7. UART通信
**配置文件定义**:
- UART0_INST
- UART_WIT_INST
- DMA相关配置

**代码使用情况**:
- ✅ BSP/Src/Serial.c: 正确使用syscfg生成的宏定义

## 🔧 已修正的问题

### 问题1: 编码器中断处理GPIO端口错误
**问题描述**: APP/Src/Interrupt.c中使用GPIOA读取编码器中断状态，但配置文件中编码器在GPIOB
**修正措施**: 
- 修正宏定义CLR_SPD_ISR_FLAG使用SPD_READER_A_PORT
- 修正中断状态读取使用SPD_READER_A_PORT
- 在Interrupt.h中添加统一宏定义

### 问题2: 电机PWM定时器实例错误
**问题描述**: BSP/Src/Motor.c中使用不存在的MotorFront_INST
**修正措施**:
- 左前轮使用MotorAFront_INST (TIMG7)
- 右前轮使用MotorBFront_INST (TIMA1)
- 修正Motor_Start函数分别启动两个定时器

### 问题3: 循迹传感器GPIO端口硬编码
**问题描述**: BSP/Src/Tracker.c中硬编码使用GPIOB
**修正措施**:
- Serial_CLK操作使用Serial_CLK_PORT (GPIOB)
- Serial_DAT操作使用Serial_DAT_PORT (GPIOA)

## 📋 验证清单

- [x] 所有GPIO操作使用syscfg生成的宏定义
- [x] 无硬编码的GPIO端口引用
- [x] 编码器中断处理正确配置
- [x] 电机PWM定时器正确配置
- [x] 电机方向控制引脚正确配置
- [x] 按键读取功能正确配置
- [x] 串行通信引脚正确配置
- [x] I2C OLED通信正确配置
- [x] UART通信正确配置

## 📝 总结

经过系统性验证，TI_CAR1.3工程中的所有引脚配置现在都与empty.syscfg配置文件保持一致。主要修正了以下问题：

1. **编码器中断处理**: 从错误的GPIOA改为正确的GPIOB
2. **电机PWM配置**: 从单定时器双通道改为双定时器单通道
3. **循迹传感器**: 消除硬编码GPIO端口引用

所有修正都确保了原工程功能的完整性，并提高了代码的可维护性。
