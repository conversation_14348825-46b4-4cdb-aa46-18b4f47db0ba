******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 24 19:11:20 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000043e9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005108  0001aef8  R  X
  SRAM                  20200000   00008000  000005e6  00007a1a  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005108   00005108    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004e00   00004e00    r-x .text
  00004ec0    00004ec0    000001f0   000001f0    r-- .rodata
  000050b0    000050b0    00000058   00000058    r-- .cinit
20200000    20200000    000003e6   00000000    rw-
  20200000    20200000    00000338   00000000    rw- .bss
  20200338    20200338    000000ae   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004e00     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000368     Interrupt.o (.text.UART1_IRQHandler)
                  00000df8    00000238     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001030    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001250    000001dc            : _printfi.c.obj (.text._pconv_g)
                  0000142c    000001b0     Task.o (.text.Task_Start)
                  000015dc    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000176e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001770    0000013c     Tracker.o (.text.Tracker_Read)
                  000018ac    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000019e8    00000134            : qsort.c.obj (.text.qsort)
                  00001b1c    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00001c40    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001d60    00000110     OLED.o (.text.OLED_Init)
                  00001e70    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001f7c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002080    000000f0     Task_App.o (.text.Task_Motor_PID)
                  00002170    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002254    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002330    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002408    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000024e0    000000b4     Task.o (.text.Task_Add)
                  00002594    000000b0     Task_App.o (.text.Task_Init)
                  00002644    000000ac     Interrupt.o (.text.GROUP1_IRQHandler)
                  000026f0    000000a4     Motor.o (.text.Motor_GetSpeed)
                  00002794    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002836    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002838    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000028d8    0000009c     Motor.o (.text.Motor_SetDuty)
                  00002974    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00002a0c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00002a98    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002b24    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00002ba8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002c2c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002cae    00000002     --HOLE-- [fill = 0]
                  00002cb0    00000080     Task_App.o (.text.Task_Serial)
                  00002d30    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002dac    00000074     Motor.o (.text.Motor_SetDirc)
                  00002e20    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002e94    0000000c     SysTick.o (.text.Sys_GetTick)
                  00002ea0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002f14    00000070     Serial.o (.text.MyPrintf_DMA)
                  00002f84    0000006c     Motor.o (.text.Motor_Start)
                  00002ff0    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000305a    00000002     --HOLE-- [fill = 0]
                  0000305c    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  000030c4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000312c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003192    00000002     --HOLE-- [fill = 0]
                  00003194    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000031f8    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000325c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000032be    00000002     --HOLE-- [fill = 0]
                  000032c0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003322    00000002     --HOLE-- [fill = 0]
                  00003324    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003384    00000060     Key_Led.o (.text.Key_Read)
                  000033e4    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003442    00000002     --HOLE-- [fill = 0]
                  00003444    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000034a0    0000005c     Task_App.o (.text.Task_Tracker)
                  000034fc    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003558    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000035b4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000360c    00000058     Serial.o (.text.Serial_Init)
                  00003664    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000036bc    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003714    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000376a    00000002     --HOLE-- [fill = 0]
                  0000376c    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  000037c0    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003812    00000002     --HOLE-- [fill = 0]
                  00003814    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00003864    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000038b4    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00003900    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000394c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003996    00000002     --HOLE-- [fill = 0]
                  00003998    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000039e0    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00003a24    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00003a68    00000044     Task_App.o (.text.Task_Key)
                  00003aac    00000044     wit.o (.text.WIT_Init)
                  00003af0    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00003b34    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00003b76    00000002     --HOLE-- [fill = 0]
                  00003b78    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003bb8    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003bf8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003c38    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00003c78    0000003e     Task.o (.text.Task_CMP)
                  00003cb6    00000002     --HOLE-- [fill = 0]
                  00003cb8    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003cf4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003d30    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003d6c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003da8    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003de4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003e20    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003e5a    00000002     --HOLE-- [fill = 0]
                  00003e5c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003e96    00000002     --HOLE-- [fill = 0]
                  00003e98    00000038     Interrupt.o (.text.Interrupt_Init)
                  00003ed0    00000038     Task_App.o (.text.Task_LED)
                  00003f08    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00003f40    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003f74    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003fa8    00000034     Task_App.o (.text.Task_IdleFunction)
                  00003fdc    00000030     Interrupt.o (.text.DL_DMA_setTransferSize)
                  0000400c    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  0000403c    00000030     wit.o (.text.DL_DMA_setTransferSize)
                  0000406c    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  0000409c    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000040cc    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000040fc    00000030            : vsnprintf.c.obj (.text._outs)
                  0000412c    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004158    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00004184    0000002c     wit.o (.text.__NVIC_EnableIRQ)
                  000041b0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000041dc    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00004206    00000028     OLED.o (.text.DL_Common_updateReg)
                  0000422e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004256    00000002     --HOLE-- [fill = 0]
                  00004258    00000028     Interrupt.o (.text.DL_DMA_setDestAddr)
                  00004280    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000042a8    00000028     wit.o (.text.DL_DMA_setDestAddr)
                  000042d0    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000042f8    00000028     wit.o (.text.DL_DMA_setSrcAddr)
                  00004320    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004348    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004370    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004398    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  000043c0    00000028     SysTick.o (.text.SysTick_Increasment)
                  000043e8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004410    00000026     Interrupt.o (.text.DL_DMA_disableChannel)
                  00004436    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  0000445c    00000026     Interrupt.o (.text.DL_DMA_enableChannel)
                  00004482    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  000044a8    00000026     wit.o (.text.DL_DMA_enableChannel)
                  000044ce    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000044f4    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000451a    00000002     --HOLE-- [fill = 0]
                  0000451c    00000024     Interrupt.o (.text.DL_DMA_getTransferSize)
                  00004540    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004564    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00004588    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000045aa    00000002     --HOLE-- [fill = 0]
                  000045ac    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000045cc    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000045ec    00000020     SysTick.o (.text.Delay)
                  0000460c    00000020     main.o (.text.main)
                  0000462c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000464a    00000002     --HOLE-- [fill = 0]
                  0000464c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000466a    00000002     --HOLE-- [fill = 0]
                  0000466c    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00004688    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000046a4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000046c0    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  000046dc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000046f8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004714    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004730    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  0000474c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00004768    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00004784    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000047a0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000047bc    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000047d8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000047f4    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00004810    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00004828    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00004840    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004858    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004870    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00004888    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000048a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000048b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000048d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000048e8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00004900    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00004918    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00004930    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00004948    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00004960    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00004978    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00004990    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000049a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000049c0    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000049d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000049f0    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00004a08    00000018     OLED.o (.text.DL_I2C_reset)
                  00004a20    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004a38    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004a50    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00004a68    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00004a80    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004a98    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004ab0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004ac8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004ae0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004af8    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00004b10    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00004b28    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00004b40    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00004b58    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00004b70    00000018     Interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  00004b88    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00004ba0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00004bb8    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  00004bd0    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00004be8    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00004c00    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00004c18    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00004c2e    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00004c44    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00004c5a    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00004c70    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00004c86    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004c9c    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00004cb0    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00004cc4    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00004cd8    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004cec    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004d00    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004d14    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004d28    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004d3c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004d50    00000014     Interrupt.o (.text.DL_UART_receiveData)
                  00004d64    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004d78    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00004d8c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00004d9e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00004db0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004dc2    00000002     --HOLE-- [fill = 0]
                  00004dc4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004dd4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004de4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00004df4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004e04    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004e14    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004e22    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004e30    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004e3e    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004e48    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004e52    00000002     --HOLE-- [fill = 0]
                  00004e54    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004e64    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004e6e    00000008     Interrupt.o (.text.SysTick_Handler)
                  00004e76    00000008     Task_App.o (.text.Task_OLED)
                  00004e7e    00000002     --HOLE-- [fill = 0]
                  00004e80    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004e88    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004e90    00000006     libc.a : exit.c.obj (.text:abort)
                  00004e96    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004e9a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004e9e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004ea2    00000002     --HOLE-- [fill = 0]
                  00004ea4    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004eb4    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00004eb8    00000008     --HOLE-- [fill = 0]

.cinit     0    000050b0    00000058     
                  000050b0    00000032     (.cinit..data.load) [load image, compression = lzss]
                  000050e2    00000002     --HOLE-- [fill = 0]
                  000050e4    0000000c     (__TI_handler_table)
                  000050f0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000050f8    00000010     (__TI_cinit_table)

.rodata    0    00004ec0    000001f0     
                  00004ec0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004fc1    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00004fc4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004fec    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00005004    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000501c    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00005034    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00005045    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005056    00000011     Task_App.o (.rodata.str1.492715258893803702.1)
                  00005067    00000001     --HOLE-- [fill = 0]
                  00005068    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00005072    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  0000507c    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00005084    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000508c    00000007     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00005093    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005099    00000005     Task_App.o (.rodata.str1.11683036942922059812.1)
                  0000509e    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000050a2    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  000050a6    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000050a8    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000050aa    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  000050ac    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  000050ae    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000338     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    00000021     (.common:wit_dmaBuffer)
                  20200311    00000003     --HOLE--
                  20200314    00000020     (.common:wit_data)
                  20200334    00000004     (.common:ExISR_Flag)

.data      0    20200338    000000ae     UNINITIALIZED
                  20200338    00000040     Motor.o (.data.Motor_Font_Left)
                  20200378    00000040     Motor.o (.data.Motor_Font_Right)
                  202003b8    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202003c0    00000008     Task_App.o (.data.Motor)
                  202003c8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202003cc    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202003d0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202003d4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202003d8    00000004     SysTick.o (.data.delayTick)
                  202003dc    00000004     SysTick.o (.data.uwTick)
                  202003e0    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202003e2    00000001     Task_App.o (.data.Flag_LED)
                  202003e3    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  202003e4    00000001     Task.o (.data.Task_Num)
                  202003e5    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3350    151       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3390    343       0      
                                                               
    .\APP\Src\
       Interrupt.o                    1522    0         5      
       Task_App.o                     864     51        32     
    +--+------------------------------+-------+---------+---------+
       Total:                         2386    51        37     
                                                               
    .\BSP\Src\
       OLED.o                         1304    0         0      
       Serial.o                       404     0         512    
       Task.o                         674     0         241    
       Motor.o                        612     0         128    
       PID_IQMath.o                   402     0         0      
       wit.o                          278     0         65     
       Tracker.o                      338     0         0      
       Key_Led.o                      118     0         0      
       SysTick.o                      84      0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         4214    0         954    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1136    0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNdiv.o                      24      0         0      
       _IQNmpy.o                      24      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5794    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2896    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       86        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   19916   771       1507   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000050f8 records: 2, size/record: 8, table size: 16
	.data: load addr=000050b0, load size=00000032 bytes, run addr=20200338, run size=000000ae bytes, compression=lzss
	.bss: load addr=000050f0, load size=00000008 bytes, run addr=20200000, run size=00000338 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000050e4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000015dd     00004e54     00004e50   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000043e9     00004ea4     00004e9e   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00004e97  ADC0_IRQHandler                      
00004e97  ADC1_IRQHandler                      
00004e97  AES_IRQHandler                       
00004e9a  C$$EXIT                              
00004e97  CANFD0_IRQHandler                    
00004e97  DAC0_IRQHandler                      
00004e3f  DL_Common_delayCycles                
000038b5  DL_DMA_initChannel                   
000033e5  DL_I2C_fillControllerTXFIFO          
000044f5  DL_I2C_setClockConfig                
00002255  DL_SYSCTL_configSYSPLL               
00003195  DL_SYSCTL_setHFCLKSourceHFXTParams   
000039e1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001f7d  DL_Timer_initFourCCPWMMode           
000047bd  DL_Timer_setCaptCompUpdateMethod     
00004ac9  DL_Timer_setCaptureCompareOutCtl     
00004dd5  DL_Timer_setCaptureCompareValue      
000047d9  DL_Timer_setClockConfig              
0000376d  DL_UART_drainRXFIFO                  
00003999  DL_UART_init                         
00004d8d  DL_UART_setClockConfig               
00004e97  DMA_IRQHandler                       
202003c8  Data_MotorEncoder                    
202003cc  Data_Motor_TarSpeed                  
202003b8  Data_Tracker_Input                   
202003d0  Data_Tracker_Offset                  
00004e97  Default_Handler                      
000045ed  Delay                                
20200334  ExISR_Flag                           
202003e2  Flag_LED                             
00004e97  GROUP0_IRQHandler                    
00002645  GROUP1_IRQHandler                    
00004e9b  HOSTexit                             
00004e97  HardFault_Handler                    
00004e97  I2C0_IRQHandler                      
00004e97  I2C1_IRQHandler                      
00002ff1  I2C_OLED_Clear                       
00002975  I2C_OLED_WR_Byte                     
00003325  I2C_OLED_i2c_sda_unlock              
00003e99  Interrupt_Init                       
00003385  Key_Read                             
202003c0  Motor                                
20200338  Motor_Font_Left                      
20200378  Motor_Font_Right                     
000026f1  Motor_GetSpeed                       
000028d9  Motor_SetDuty                        
00002f85  Motor_Start                          
00002f15  MyPrintf_DMA                         
00004e97  NMI_Handler                          
00001d61  OLED_Init                            
000041dd  PID_IQ_Init                          
00001b1d  PID_IQ_Prosc                         
00003a25  PID_IQ_SetParams                     
00004e97  PendSV_Handler                       
00004e97  RTC_IRQHandler                       
00004e9f  Reset_Handler                        
00004e97  SPI0_IRQHandler                      
00004e97  SPI1_IRQHandler                      
00004e97  SVC_Handler                          
0000406d  SYSCFG_DL_DMA_CH_RX_init             
00004ba1  SYSCFG_DL_DMA_CH_TX_init             
00004bb9  SYSCFG_DL_DMA_WIT_init               
00004de5  SYSCFG_DL_DMA_init                   
00000df9  SYSCFG_DL_GPIO_init                  
000035b5  SYSCFG_DL_I2C_MPU6050_init           
000031f9  SYSCFG_DL_I2C_OLED_init              
00002a0d  SYSCFG_DL_MotorFront_init            
00003445  SYSCFG_DL_SYSCTL_init                
00004df5  SYSCFG_DL_SYSTICK_init               
00002b25  SYSCFG_DL_UART0_init                 
0000305d  SYSCFG_DL_UART_WIT_init              
0000412d  SYSCFG_DL_init                       
00002839  SYSCFG_DL_initPower                  
0000360d  Serial_Init                          
20200000  Serial_RxData                        
00004e6f  SysTick_Handler                      
000043c1  SysTick_Increasment                  
00002e95  Sys_GetTick                          
00004e97  TIMA0_IRQHandler                     
00004e97  TIMA1_IRQHandler                     
00004e97  TIMG0_IRQHandler                     
00004e97  TIMG12_IRQHandler                    
00004e97  TIMG6_IRQHandler                     
00004e97  TIMG7_IRQHandler                     
00004e97  TIMG8_IRQHandler                     
00004d9f  TI_memcpy_small                      
00004e31  TI_memset_small                      
000024e1  Task_Add                             
00003fa9  Task_IdleFunction                    
00002595  Task_Init                            
00003a69  Task_Key                             
00003ed1  Task_LED                             
00002081  Task_Motor_PID                       
00004e77  Task_OLED                            
00002cb1  Task_Serial                          
0000142d  Task_Start                           
000034a1  Task_Tracker                         
00001771  Tracker_Read                         
00004e97  UART0_IRQHandler                     
00000a91  UART1_IRQHandler                     
00004e97  UART2_IRQHandler                     
00004e97  UART3_IRQHandler                     
00003aad  WIT_Init                             
00004bd1  _IQ24div                             
00004be9  _IQ24mpy                             
0000409d  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000050f8  __TI_CINIT_Base                      
00005108  __TI_CINIT_Limit                     
00005108  __TI_CINIT_Warm                      
000050e4  __TI_Handler_Table_Base              
000050f0  __TI_Handler_Table_Limit             
00003de5  __TI_auto_init_nobinit_nopinit       
00002d31  __TI_decompress_lzss                 
00004db1  __TI_decompress_none                 
00003665  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004c87  __TI_zero_init_nomemset              
000015e7  __adddf3                             
00002413  __addsf3                             
00004ec0  __aeabi_ctype_table_                 
00004ec0  __aeabi_ctype_table_C                
00002ea1  __aeabi_d2f                          
0000394d  __aeabi_d2iz                         
00003b35  __aeabi_d2uiz                        
000015e7  __aeabi_dadd                         
0000325d  __aeabi_dcmpeq                       
00003299  __aeabi_dcmpge                       
000032ad  __aeabi_dcmpgt                       
00003285  __aeabi_dcmple                       
00003271  __aeabi_dcmplt                       
00001e71  __aeabi_ddiv                         
00002171  __aeabi_dmul                         
000015dd  __aeabi_dsub                         
202003d4  __aeabi_errno                        
00004e81  __aeabi_errno_addr                   
00003bb9  __aeabi_f2d                          
00003f09  __aeabi_f2iz                         
00002413  __aeabi_fadd                         
000032c1  __aeabi_fcmpeq                       
000032fd  __aeabi_fcmpge                       
00003311  __aeabi_fcmpgt                       
000032e9  __aeabi_fcmple                       
000032d5  __aeabi_fcmplt                       
00002c2d  __aeabi_fdiv                         
00002a99  __aeabi_fmul                         
00002409  __aeabi_fsub                         
000041b1  __aeabi_i2d                          
00003d6d  __aeabi_i2f                          
00003715  __aeabi_idiv                         
0000176f  __aeabi_idiv0                        
00003715  __aeabi_idivmod                      
00002837  __aeabi_ldiv0                        
0000464d  __aeabi_llsl                         
00004565  __aeabi_lmul                         
00004e89  __aeabi_memcpy                       
00004e89  __aeabi_memcpy4                      
00004e89  __aeabi_memcpy8                      
00004e15  __aeabi_memset                       
00004e15  __aeabi_memset4                      
00004e15  __aeabi_memset8                      
00003b79  __aeabi_uidiv                        
00003b79  __aeabi_uidivmod                     
00004d65  __aeabi_uldivmod                     
0000464d  __ashldi3                            
ffffffff  __binit__                            
000030c5  __cmpdf2                             
00003e21  __cmpsf2                             
00001e71  __divdf3                             
00002c2d  __divsf3                             
000030c5  __eqdf2                              
00003e21  __eqsf2                              
00003bb9  __extendsfdf2                        
0000394d  __fixdfsi                            
00003f09  __fixsfsi                            
00003b35  __fixunsdfsi                         
000041b1  __floatsidf                          
00003d6d  __floatsisf                          
00002e21  __gedf2                              
00003da9  __gesf2                              
00002e21  __gtdf2                              
00003da9  __gtsf2                              
000030c5  __ledf2                              
00003e21  __lesf2                              
000030c5  __ltdf2                              
00003e21  __ltsf2                              
UNDEFED   __mpu_init                           
00002171  __muldf3                             
00004565  __muldi3                             
00003e5d  __muldsi3                            
00002a99  __mulsf3                             
000030c5  __nedf2                              
00003e21  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000015dd  __subdf3                             
00002409  __subsf3                             
00002ea1  __truncdfsf2                         
00002795  __udivmoddi4                         
000043e9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00004eb5  _system_pre_init                     
00004e91  abort                                
00003bf9  atoi                                 
ffffffff  binit                                
202003d8  delayTick                            
202003e5  enable_group1_irq                    
000034fd  frexp                                
000034fd  frexpl                               
00000000  interruptVectors                     
00002331  ldexp                                
00002331  ldexpl                               
0000460d  main                                 
00004589  memccpy                              
000019e9  qsort                                
00002331  scalbn                               
00002331  scalbnl                              
202003dc  uwTick                               
00003c39  vsnprintf                            
00004e05  wcslen                               
20200314  wit_data                             
202002f0  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  UART1_IRQHandler                     
00000df9  SYSCFG_DL_GPIO_init                  
0000142d  Task_Start                           
000015dd  __aeabi_dsub                         
000015dd  __subdf3                             
000015e7  __adddf3                             
000015e7  __aeabi_dadd                         
0000176f  __aeabi_idiv0                        
00001771  Tracker_Read                         
000019e9  qsort                                
00001b1d  PID_IQ_Prosc                         
00001d61  OLED_Init                            
00001e71  __aeabi_ddiv                         
00001e71  __divdf3                             
00001f7d  DL_Timer_initFourCCPWMMode           
00002081  Task_Motor_PID                       
00002171  __aeabi_dmul                         
00002171  __muldf3                             
00002255  DL_SYSCTL_configSYSPLL               
00002331  ldexp                                
00002331  ldexpl                               
00002331  scalbn                               
00002331  scalbnl                              
00002409  __aeabi_fsub                         
00002409  __subsf3                             
00002413  __addsf3                             
00002413  __aeabi_fadd                         
000024e1  Task_Add                             
00002595  Task_Init                            
00002645  GROUP1_IRQHandler                    
000026f1  Motor_GetSpeed                       
00002795  __udivmoddi4                         
00002837  __aeabi_ldiv0                        
00002839  SYSCFG_DL_initPower                  
000028d9  Motor_SetDuty                        
00002975  I2C_OLED_WR_Byte                     
00002a0d  SYSCFG_DL_MotorFront_init            
00002a99  __aeabi_fmul                         
00002a99  __mulsf3                             
00002b25  SYSCFG_DL_UART0_init                 
00002c2d  __aeabi_fdiv                         
00002c2d  __divsf3                             
00002cb1  Task_Serial                          
00002d31  __TI_decompress_lzss                 
00002e21  __gedf2                              
00002e21  __gtdf2                              
00002e95  Sys_GetTick                          
00002ea1  __aeabi_d2f                          
00002ea1  __truncdfsf2                         
00002f15  MyPrintf_DMA                         
00002f85  Motor_Start                          
00002ff1  I2C_OLED_Clear                       
0000305d  SYSCFG_DL_UART_WIT_init              
000030c5  __cmpdf2                             
000030c5  __eqdf2                              
000030c5  __ledf2                              
000030c5  __ltdf2                              
000030c5  __nedf2                              
00003195  DL_SYSCTL_setHFCLKSourceHFXTParams   
000031f9  SYSCFG_DL_I2C_OLED_init              
0000325d  __aeabi_dcmpeq                       
00003271  __aeabi_dcmplt                       
00003285  __aeabi_dcmple                       
00003299  __aeabi_dcmpge                       
000032ad  __aeabi_dcmpgt                       
000032c1  __aeabi_fcmpeq                       
000032d5  __aeabi_fcmplt                       
000032e9  __aeabi_fcmple                       
000032fd  __aeabi_fcmpge                       
00003311  __aeabi_fcmpgt                       
00003325  I2C_OLED_i2c_sda_unlock              
00003385  Key_Read                             
000033e5  DL_I2C_fillControllerTXFIFO          
00003445  SYSCFG_DL_SYSCTL_init                
000034a1  Task_Tracker                         
000034fd  frexp                                
000034fd  frexpl                               
000035b5  SYSCFG_DL_I2C_MPU6050_init           
0000360d  Serial_Init                          
00003665  __TI_ltoa                            
00003715  __aeabi_idiv                         
00003715  __aeabi_idivmod                      
0000376d  DL_UART_drainRXFIFO                  
000038b5  DL_DMA_initChannel                   
0000394d  __aeabi_d2iz                         
0000394d  __fixdfsi                            
00003999  DL_UART_init                         
000039e1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003a25  PID_IQ_SetParams                     
00003a69  Task_Key                             
00003aad  WIT_Init                             
00003b35  __aeabi_d2uiz                        
00003b35  __fixunsdfsi                         
00003b79  __aeabi_uidiv                        
00003b79  __aeabi_uidivmod                     
00003bb9  __aeabi_f2d                          
00003bb9  __extendsfdf2                        
00003bf9  atoi                                 
00003c39  vsnprintf                            
00003d6d  __aeabi_i2f                          
00003d6d  __floatsisf                          
00003da9  __gesf2                              
00003da9  __gtsf2                              
00003de5  __TI_auto_init_nobinit_nopinit       
00003e21  __cmpsf2                             
00003e21  __eqsf2                              
00003e21  __lesf2                              
00003e21  __ltsf2                              
00003e21  __nesf2                              
00003e5d  __muldsi3                            
00003e99  Interrupt_Init                       
00003ed1  Task_LED                             
00003f09  __aeabi_f2iz                         
00003f09  __fixsfsi                            
00003fa9  Task_IdleFunction                    
0000406d  SYSCFG_DL_DMA_CH_RX_init             
0000409d  _IQ24toF                             
0000412d  SYSCFG_DL_init                       
000041b1  __aeabi_i2d                          
000041b1  __floatsidf                          
000041dd  PID_IQ_Init                          
000043c1  SysTick_Increasment                  
000043e9  _c_int00_noargs                      
000044f5  DL_I2C_setClockConfig                
00004565  __aeabi_lmul                         
00004565  __muldi3                             
00004589  memccpy                              
000045ed  Delay                                
0000460d  main                                 
0000464d  __aeabi_llsl                         
0000464d  __ashldi3                            
000047bd  DL_Timer_setCaptCompUpdateMethod     
000047d9  DL_Timer_setClockConfig              
00004ac9  DL_Timer_setCaptureCompareOutCtl     
00004ba1  SYSCFG_DL_DMA_CH_TX_init             
00004bb9  SYSCFG_DL_DMA_WIT_init               
00004bd1  _IQ24div                             
00004be9  _IQ24mpy                             
00004c87  __TI_zero_init_nomemset              
00004d65  __aeabi_uldivmod                     
00004d8d  DL_UART_setClockConfig               
00004d9f  TI_memcpy_small                      
00004db1  __TI_decompress_none                 
00004dd5  DL_Timer_setCaptureCompareValue      
00004de5  SYSCFG_DL_DMA_init                   
00004df5  SYSCFG_DL_SYSTICK_init               
00004e05  wcslen                               
00004e15  __aeabi_memset                       
00004e15  __aeabi_memset4                      
00004e15  __aeabi_memset8                      
00004e31  TI_memset_small                      
00004e3f  DL_Common_delayCycles                
00004e6f  SysTick_Handler                      
00004e77  Task_OLED                            
00004e81  __aeabi_errno_addr                   
00004e89  __aeabi_memcpy                       
00004e89  __aeabi_memcpy4                      
00004e89  __aeabi_memcpy8                      
00004e91  abort                                
00004e97  ADC0_IRQHandler                      
00004e97  ADC1_IRQHandler                      
00004e97  AES_IRQHandler                       
00004e97  CANFD0_IRQHandler                    
00004e97  DAC0_IRQHandler                      
00004e97  DMA_IRQHandler                       
00004e97  Default_Handler                      
00004e97  GROUP0_IRQHandler                    
00004e97  HardFault_Handler                    
00004e97  I2C0_IRQHandler                      
00004e97  I2C1_IRQHandler                      
00004e97  NMI_Handler                          
00004e97  PendSV_Handler                       
00004e97  RTC_IRQHandler                       
00004e97  SPI0_IRQHandler                      
00004e97  SPI1_IRQHandler                      
00004e97  SVC_Handler                          
00004e97  TIMA0_IRQHandler                     
00004e97  TIMA1_IRQHandler                     
00004e97  TIMG0_IRQHandler                     
00004e97  TIMG12_IRQHandler                    
00004e97  TIMG6_IRQHandler                     
00004e97  TIMG7_IRQHandler                     
00004e97  TIMG8_IRQHandler                     
00004e97  UART0_IRQHandler                     
00004e97  UART2_IRQHandler                     
00004e97  UART3_IRQHandler                     
00004e9a  C$$EXIT                              
00004e9b  HOSTexit                             
00004e9f  Reset_Handler                        
00004eb5  _system_pre_init                     
00004ec0  __aeabi_ctype_table_                 
00004ec0  __aeabi_ctype_table_C                
000050e4  __TI_Handler_Table_Base              
000050f0  __TI_Handler_Table_Limit             
000050f8  __TI_CINIT_Base                      
00005108  __TI_CINIT_Limit                     
00005108  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  wit_dmaBuffer                        
20200314  wit_data                             
20200334  ExISR_Flag                           
20200338  Motor_Font_Left                      
20200378  Motor_Font_Right                     
202003b8  Data_Tracker_Input                   
202003c0  Motor                                
202003c8  Data_MotorEncoder                    
202003cc  Data_Motor_TarSpeed                  
202003d0  Data_Tracker_Offset                  
202003d4  __aeabi_errno                        
202003d8  delayTick                            
202003dc  uwTick                               
202003e2  Flag_LED                             
202003e5  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[239 symbols]
