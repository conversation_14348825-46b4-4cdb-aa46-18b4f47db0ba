#include "Motor.h"

#define SET_FOWARD(X)   DL_GPIO_setPins(DIRC_CTRL_PORT, (X))
#define SET_BACKWARD(X) DL_GPIO_clearPins(DIRC_CTRL_PORT, (X))

//左前轮
MOTOR_Def_t Motor_Font_Left = {.Motor_Dirc = DIRC_FOWARD,
                               .Motor_Encoder_Addr = &Data_MotorEncoder[0],
                               .Motor_Turn_Pin = DIRC_CTRL_FONT_LEFT_PIN,
                               .Motor_PWM_TIMX = MotorFront_INST,
                               .Motor_PWM_CH = DL_TIMER_CC_0_INDEX};
//右前轮
MOTOR_Def_t Motor_Font_Right = {.Motor_Dirc = DIRC_FOWARD,
                                .Motor_Encoder_Addr = &Data_MotorEncoder[1],
                                .Motor_Turn_Pin = DIRC_CTRL_FONT_RIGHT_PIN,
                                .Motor_PWM_TIMX = MotorFront_INST,
                                .Motor_PWM_CH = DL_TIMER_CC_1_INDEX};
/**
 * @brief 开启电机
 * 
 */
void Motor_Start(void)
{
    DL_TimerG_startCounter(MotorFront_INST); //开启前轮

    /*设置所有占空比都为0*/
    Motor_SetDuty(&Motor_Font_Left, 0.0f);
    Motor_SetDuty(&Motor_Font_Right, 0.0f);

    /*初始化所有PID对象*/
    PID_Init(&Motor_Font_Left.Motor_PID_Instance);
    PID_Init(&Motor_Font_Right.Motor_PID_Instance);

    /*PID 系数初值*/
    PID_SetParams(&Motor_Font_Left.Motor_PID_Instance, 2.0f, 1.0f, 0.15f);
    PID_SetParams(&Motor_Font_Right.Motor_PID_Instance, 2.0f, 1.0f, 0.15f);
}

/**
 * @brief 设置电机正反转
 * 
 * @param Motor_Num 电机编号
 * @param DIRC 方向
 * @return 返回设置成功与否
 */
static bool Motor_SetDirc(MOTOR_Def_t *Motor, Motor_DIRC_Def_t Dirc)
{
    if (Motor == NULL) return false;

    if (Dirc == DIRC_FOWARD)
    {
        SET_FOWARD(Motor->Motor_Turn_Pin);
        Motor->Motor_Dirc = DIRC_FOWARD;
        return true;
    }
    else if (Dirc == DIRC_BACKWARD)
    {
        SET_BACKWARD(Motor->Motor_Turn_Pin);
        Motor->Motor_Dirc = DIRC_BACKWARD;
        return true;
    }
    return false;
}

/**
 * @brief 设置对应的电机占空比值
 * 
 * @param Motor  电机编号
 * @param value PWM占空比 (-100 ~ +100, 负值反转，正值正转)
 * @return 返回设置成功与否
 */
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value)
{
    if (Motor == NULL) return false;

    // 限制范围
    if (value > 100.0f) value = 100.0f;
    if (value < -100.0f) value = -100.0f;

    // 根据符号设置方向
    if (value >= 0)
    {
        Motor_SetDirc(Motor, DIRC_FOWARD);
    }
    else
    {
        Motor_SetDirc(Motor, DIRC_BACKWARD);
    }

    // 设置PWM占空比（使用绝对值）
    uint32_t duty = (uint32_t)fabs(value);
    DL_TimerG_setCaptureCompareValue(Motor->Motor_PWM_TIMX, duty, Motor->Motor_PWM_CH);

    return true;
}

/**
 * @brief 获取电机速度 更新到PID实例中
 * 
 * @param Motor 电机
 * @param time 读取时间间隔 ms
 * @return true 
 * @return false 
 */
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time)
{
    if (Motor == NULL) return false;

    Motor->Motor_PID_Instance.Acutal_Now =*Motor->Motor_Encoder_Addr;
    *Motor->Motor_Encoder_Addr = 0;

    return true;
}





