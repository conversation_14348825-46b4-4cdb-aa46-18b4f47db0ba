/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const Board         = scripting.addModule("/ti/driverlib/Board");
const GPIO          = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1         = GPIO.addInstance();
const GPIO2         = GPIO.addInstance();
const GPIO3         = GPIO.addInstance();
const GPIO4         = GPIO.addInstance();
const GPIO5         = GPIO.addInstance();
const GPIO6         = GPIO.addInstance();
const GPIO7         = GPIO.addInstance();
const GPIO8         = GPIO.addInstance();
const I2C           = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1          = I2C.addInstance();
const MATHACL       = scripting.addModule("/ti/driverlib/MATHACL");
const PWM           = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1          = PWM.addInstance();
const PWM2          = PWM.addInstance();
const SYSCTL        = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK       = scripting.addModule("/ti/driverlib/SYSTICK");
const UART          = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1         = UART.addInstance();
const UART2         = UART.addInstance();
const UART3         = UART.addInstance();
const UART4         = UART.addInstance();
const ProjectConfig = scripting.addModule("/ti/project_config/ProjectConfig");

/**
 * Write custom configuration values to the imported modules.
 */
const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 4;

const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 10;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const pinFunction4     = system.clockTree["HFXT"];
pinFunction4.inputFreq = 40;
pinFunction4.enable    = true;


GPIO1.$name                          = "LED";
GPIO1.associatedPins.create(3);
GPIO1.associatedPins[0].assignedPin  = "14";
GPIO1.associatedPins[0].$name        = "Board";
GPIO1.associatedPins[0].assignedPort = "PORTA";
GPIO1.associatedPins[1].initialValue = "SET";
GPIO1.associatedPins[1].ioStructure  = "SD";
GPIO1.associatedPins[1].$name        = "RED";
GPIO1.associatedPins[1].assignedPort = "PORTB";
GPIO1.associatedPins[1].assignedPin  = "23";
GPIO1.associatedPins[2].$name        = "BLUE";
GPIO1.associatedPins[2].ioStructure  = "SD";
GPIO1.associatedPins[2].initialValue = "SET";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].assignedPin  = "22";

GPIO2.$name                              = "KEY";
GPIO2.associatedPins.create(3);
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].$name            = "KEY4";
GPIO2.associatedPins[0].assignedPort     = "PORTA";
GPIO2.associatedPins[0].assignedPin      = "26";
GPIO2.associatedPins[1].direction        = "INPUT";
GPIO2.associatedPins[1].internalResistor = "PULL_UP";
GPIO2.associatedPins[1].ioStructure      = "SD";
GPIO2.associatedPins[1].$name            = "KEY2";
GPIO2.associatedPins[1].assignedPort     = "PORTB";
GPIO2.associatedPins[1].assignedPin      = "24";
GPIO2.associatedPins[2].direction        = "INPUT";
GPIO2.associatedPins[2].ioStructure      = "SD";
GPIO2.associatedPins[2].internalResistor = "PULL_UP";
GPIO2.associatedPins[2].assignedPort     = "PORTA";
GPIO2.associatedPins[2].assignedPin      = "27";
GPIO2.associatedPins[2].$name            = "KEY3";

GPIO3.$name                               = "SPD_READER_A";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].direction         = "INPUT";
GPIO3.associatedPins[0].interruptEn       = true;
GPIO3.associatedPins[0].interruptPriority = "1";
GPIO3.associatedPins[0].$name             = "FONT_LEFT_A";
GPIO3.associatedPins[0].polarity          = "RISE";
GPIO3.associatedPins[0].internalResistor  = "PULL_DOWN";
GPIO3.associatedPins[0].assignedPort      = "PORTB";
GPIO3.associatedPins[0].assignedPin       = "11";
GPIO3.associatedPins[0].pin.$assign       = "PB11";
GPIO3.associatedPins[1].direction         = "INPUT";
GPIO3.associatedPins[1].interruptEn       = true;
GPIO3.associatedPins[1].interruptPriority = "1";
GPIO3.associatedPins[1].$name             = "FONT_RIGHT_A";
GPIO3.associatedPins[1].polarity          = "RISE";
GPIO3.associatedPins[1].internalResistor  = "PULL_DOWN";
GPIO3.associatedPins[1].assignedPort      = "PORTB";
GPIO3.associatedPins[1].assignedPin       = "9";
GPIO3.associatedPins[1].pin.$assign       = "PB9";

GPIO4.$name                              = "SPD_READER_B";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name            = "FONT_LEFT_B";
GPIO4.associatedPins[0].direction        = "INPUT";
GPIO4.associatedPins[0].ioStructure      = "SD";
GPIO4.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO4.associatedPins[0].assignedPort     = "PORTB";
GPIO4.associatedPins[0].assignedPin      = "10";
GPIO4.associatedPins[1].$name            = "FONT_RIGHT_B";
GPIO4.associatedPins[1].direction        = "INPUT";
GPIO4.associatedPins[1].ioStructure      = "SD";
GPIO4.associatedPins[1].internalResistor = "PULL_DOWN";
GPIO4.associatedPins[1].assignedPort     = "PORTB";
GPIO4.associatedPins[1].assignedPin      = "8";
GPIO4.associatedPins[1].pin.$assign      = "PB8";

GPIO5.$name                          = "DIRC_CTRL";
GPIO5.port                           = "PORTA";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name        = "FONT_LEFT";
GPIO5.associatedPins[0].ioStructure  = "SD";
GPIO5.associatedPins[0].initialValue = "SET";
GPIO5.associatedPins[0].assignedPin  = "23";
GPIO5.associatedPins[1].$name        = "FONT_RIGHT";
GPIO5.associatedPins[1].ioStructure  = "SD";
GPIO5.associatedPins[1].initialValue = "SET";
GPIO5.associatedPins[1].assignedPin  = "22";

GPIO6.$name                          = "Serial";
GPIO6.associatedPins.create(2);
GPIO6.associatedPins[0].initialValue = "SET";
GPIO6.associatedPins[0].$name        = "DAT";
GPIO6.associatedPins[0].direction    = "INPUT";
GPIO6.associatedPins[0].assignedPort = "PORTA";
GPIO6.associatedPins[0].assignedPin  = "24";
GPIO6.associatedPins[1].$name        = "CLK";
GPIO6.associatedPins[1].assignedPort = "PORTB";
GPIO6.associatedPins[1].assignedPin  = "20";
GPIO6.associatedPins[1].pin.$assign  = "PB20";

GPIO7.$name                          = "BUZZ";
GPIO7.associatedPins[0].assignedPort = "PORTA";
GPIO7.associatedPins[0].ioStructure  = "SD";
GPIO7.associatedPins[0].assignedPin  = "3";
GPIO7.associatedPins[0].$name        = "Periph";

GPIO8.$name                               = "GPIO_MPU6050";
GPIO8.port                                = "PORTA";
GPIO8.portSegment                         = "Upper";
GPIO8.associatedPins[0].$name             = "PIN_INT";
GPIO8.associatedPins[0].direction         = "INPUT";
GPIO8.associatedPins[0].assignedPin       = "30";
GPIO8.associatedPins[0].interruptEn       = true;
GPIO8.associatedPins[0].internalResistor  = "PULL_UP";
GPIO8.associatedPins[0].interruptPriority = "1";
GPIO8.associatedPins[0].polarity          = "FALL";

I2C1.basicEnableController             = true;
I2C1.basicControllerStandardBusSpeed   = "Fast";
I2C1.$name                             = "I2C_OLED";
I2C1.intController                     = ["NACK","RX_DONE","TX_DONE"];
I2C1.peripheral.sdaPin.$assign         = "PA0";
I2C1.peripheral.sclPin.$assign         = "PA1";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";

PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.timerStartTimer                    = true;
PWM1.timerCount                         = 100;
PWM1.clockPrescale                      = 40;
PWM1.$name                              = "MotorAFront";
PWM1.peripheral.$assign                 = "TIMG7";
PWM1.peripheral.ccp0Pin.$assign         = "PB15";
PWM1.peripheral.ccp1Pin.$assign         = "PB16";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle            = 1;
PWM1.PWM_CHANNEL_0.ccValue              = 1;
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.dutyCycle            = 2;
PWM1.PWM_CHANNEL_1.ccValue              = 2;
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";

PWM2.$name                              = "MotorBFront";
PWM2.clockPrescale                      = 40;
PWM2.peripheral.ccp0Pin.$assign         = "PB2";
PWM2.peripheral.ccp1Pin.$assign         = "PB3";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric10";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric12";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable      = true;
SYSTICK.interruptEnable   = true;
SYSTICK.systickEnable     = true;
SYSTICK.period            = 80000;
SYSTICK.interruptPriority = "0";

UART1.targetBaudRate                   = 115200;
UART1.enableFIFO                       = true;
UART1.rxFifoThreshold                  = "DL_UART_RX_FIFO_LEVEL_3_4_FULL";
UART1.rxTimeoutValue                   = 15;
UART1.enabledInterrupts                = ["DMA_DONE_RX","DMA_DONE_TX","EOT_DONE","RX_TIMEOUT_ERROR"];
UART1.enabledDMARXTriggers             = "DL_UART_DMA_INTERRUPT_RX_TIMEOUT";
UART1.enabledDMATXTriggers             = "DL_UART_DMA_INTERRUPT_TX";
UART1.interruptPriority                = "2";
UART1.$name                            = "UART_K230";
UART1.peripheral.rxPin.$assign         = "PB18";
UART1.peripheral.txPin.$assign         = "PB17";
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
UART1.DMA_CHANNEL_RX.enableInterrupt   = true;
UART1.DMA_CHANNEL_RX.srcLength         = "BYTE";
UART1.DMA_CHANNEL_RX.dstLength         = "BYTE";
UART1.DMA_CHANNEL_RX.$name             = "DMA_CH_RX";
UART1.DMA_CHANNEL_RX.transferMode      = "FULL_CH_REPEAT_SINGLE";
UART1.DMA_CHANNEL_RX.addressMode       = "f2b";
UART1.DMA_CHANNEL_RX.interruptPriority = "1";
UART1.DMA_CHANNEL_TX.addressMode       = "b2f";
UART1.DMA_CHANNEL_TX.srcLength         = "BYTE";
UART1.DMA_CHANNEL_TX.dstLength         = "BYTE";
UART1.DMA_CHANNEL_TX.$name             = "DMA_CH_TX";

UART2.$name                            = "UART_WIT";
UART2.targetBaudRate                   = 115200;
UART2.direction                        = "RX";
UART2.enableFIFO                       = true;
UART2.enabledInterrupts                = ["RX_TIMEOUT_ERROR"];
UART2.enabledDMARXTriggers             = "DL_UART_DMA_INTERRUPT_RX";
UART2.interruptPriority                = "0";
UART2.rxTimeoutValue                   = 5;
UART2.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric11";
UART2.peripheral.$assign               = "UART0";
UART2.peripheral.rxPin.$assign         = "PA11";
UART2.DMA_CHANNEL_RX.$name             = "DMA_WIT";
UART2.DMA_CHANNEL_RX.addressMode       = "f2b";
UART2.DMA_CHANNEL_RX.srcLength         = "BYTE";
UART2.DMA_CHANNEL_RX.dstLength         = "BYTE";
UART2.DMA_CHANNEL_RX.transferMode      = "FULL_CH_REPEAT_SINGLE";

UART3.$name                            = "UART_bujingA";
UART3.peripheral.rxPin.$assign         = "PB5";
UART3.peripheral.txPin.$assign         = "PB4";
UART3.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART3.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART3.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART3.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART3.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
UART3.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART3.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART3.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART3.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

UART4.$name                            = "UART_bujingB";
UART4.peripheral.rxPin.$assign         = "PB13";
UART4.peripheral.txPin.$assign         = "PB12";
UART4.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART4.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART4.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART4.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART4.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
UART4.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART4.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART4.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART4.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";

ProjectConfig.genLibIQ = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution       = "PA14";
GPIO1.associatedPins[1].pin.$suggestSolution       = "PB23";
GPIO1.associatedPins[2].pin.$suggestSolution       = "PB22";
GPIO2.associatedPins[0].pin.$suggestSolution       = "PA26";
GPIO2.associatedPins[1].pin.$suggestSolution       = "PB24";
GPIO2.associatedPins[2].pin.$suggestSolution       = "PA27";
GPIO4.associatedPins[0].pin.$suggestSolution       = "PB10";
GPIO5.associatedPins[0].pin.$suggestSolution       = "PA23";
GPIO5.associatedPins[1].pin.$suggestSolution       = "PA22";
GPIO6.associatedPins[0].pin.$suggestSolution       = "PA24";
GPIO7.associatedPins[0].pin.$suggestSolution       = "PA3";
GPIO8.associatedPins[0].pin.$suggestSolution       = "PA30";
I2C1.peripheral.$suggestSolution                   = "I2C0";
PWM2.peripheral.$suggestSolution                   = "TIMA1";
UART1.peripheral.$suggestSolution                  = "UART2";
UART1.DMA_CHANNEL_RX.peripheral.$suggestSolution   = "DMA_CH2";
UART1.DMA_CHANNEL_TX.peripheral.$suggestSolution   = "DMA_CH1";
UART2.DMA_CHANNEL_RX.peripheral.$suggestSolution   = "DMA_CH0";
UART3.peripheral.$suggestSolution                  = "UART1";
UART4.peripheral.$suggestSolution                  = "UART3";
