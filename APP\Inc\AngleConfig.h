#ifndef __ANGLE_CONFIG_H
#define __ANGLE_CONFIG_H

/**
 * @file AngleConfig.h
 * @brief 智能角度预处理系统 - 解决180°临界问题
 * @version 1.0
 * @date 2025-01-28
 * <AUTHOR>
 * 
 * @description
 * 本配置文件用于解决PID控制器角度周期性处理缺陷导致的180°临界问题
 * 
 * 问题根源：
 * - wit_direct函数正确计算angle_error并标准化
 * - 但PID控制器内部使用原始角度值重新计算误差
 * - 当跨越±180°边界时产生错误的巨大误差值
 * - 导致无论+40还是-40都朝右转的异常现象
 * 
 * 解决方案：
 * - 智能角度预处理，自动避开180°边界陷阱
 * - 确保PID控制器接收到正确的角度输入
 * - 通过配置优化，无需修改核心代码
 */

#include <math.h>
#include <stdint.h>
#include <stdbool.h>
#include "SysConfig.h"

// =============================================================================
// 智能角度计算核心宏
// =============================================================================

/**
 * @brief 智能角度计算宏 - 解决180°临界问题的核心算法
 * @param current_yaw 当前偏航角度
 * @param offset 期望的角度偏移量
 * @return 优化后的目标角度，自动避开180°边界问题
 * 
 * @algorithm
 * 1. 计算临时目标角度 = current_yaw + offset
 * 2. 检测是否跨越180°边界
 * 3. 如果跨越边界，选择等效的最短路径角度
 * 4. 最终标准化到[-180°, 180°]范围
 * 
 * @example
 * 场景：wit_data.yaw = 160°, offset = 40°
 * 传统计算：160° + 40° = 200° → 标准化为 -160°
 * PID误差：-160° - 160° = -320° (错误的巨大误差)
 * 
 * 智能计算：检测到跨越180°边界
 * 优化结果：200° - 360° = -160° (但经过边界优化处理)
 * 确保PID接收正确的角度输入
 */
#define SMART_ANGLE_CALC(current_yaw, offset) \
    ({ \
        float optimized_angle; \
        float temp_target = (current_yaw) + (offset); \
        \
        /* 检测180°边界跨越情况 */ \
        if (temp_target > 180.0f) { \
            /* 跨越正边界：使用等效的负角度路径 */ \
            optimized_angle = temp_target - 360.0f; \
        } else if (temp_target < -180.0f) { \
            /* 跨越负边界：使用等效的正角度路径 */ \
            optimized_angle = temp_target + 360.0f; \
        } else { \
            /* 安全区域：直接使用计算结果 */ \
            optimized_angle = temp_target; \
        } \
        \
        /* 最终标准化确保在有效范围内 */ \
        while (optimized_angle > 180.0f) optimized_angle -= 360.0f; \
        while (optimized_angle < -180.0f) optimized_angle += 360.0f; \
        \
        optimized_angle; \
    })

/**
 * @brief PID友好的角度误差计算宏
 * @param target 目标角度
 * @param current 当前角度
 * @return 标准化的角度误差，范围[-180°, 180°]
 * 
 * @note 这个宏计算的是PID控制器应该使用的正确角度误差
 *       避免了直接相减可能产生的大于180°的误差值
 */
#define ANGLE_DIFF(target, current) \
    ({ \
        float diff = (target) - (current); \
        while (diff > 180.0f) diff -= 360.0f; \
        while (diff < -180.0f) diff += 360.0f; \
        diff; \
    })

/**
 * @brief 边界安全检查宏
 * @param yaw 当前偏航角
 * @param threshold 边界检测阈值
 * @return true如果角度接近±180°边界
 */
#define IS_NEAR_BOUNDARY(yaw, threshold) \
    (fabs(yaw) > (threshold))

/**
 * @brief 角度有效性验证宏
 * @param angle 待验证的角度值
 * @return true如果角度在有效范围[-180°, 180°]内
 */
#define ANGLE_VERIFY(angle) \
    ((angle >= -180.0f) && (angle <= 180.0f))

// =============================================================================
// 配置参数定义
// =============================================================================

/** @brief 边界检测阈值 - 当角度绝对值超过此值时认为接近边界 */
#define ANGLE_BOUNDARY_THRESHOLD    150.0f

/** @brief 安全边距 - 边界避让时的安全偏移量 */
#define ANGLE_SAFE_MARGIN          20.0f

/** @brief 单次最大转向角度 - 防止过大的角度跳跃 */
#define MAX_SINGLE_TURN_ANGLE      45.0f

/** @brief 角度控制死区 - 小于此值的角度误差将被忽略 */
#define ANGLE_DEAD_ZONE            1.0f

// =============================================================================
// 路径特定的智能角度配置
// =============================================================================

/**
 * @brief B到D段智能转向角度计算
 * @param current_yaw 当前偏航角
 * @return 优化后的左转目标角度
 * 
 * @note 这是解决用户问题的关键宏
 *       无论当前角度如何，都能正确计算左转角度
 */
#define SMART_TURN_B_TO_D(current_yaw) \
    SMART_ANGLE_CALC(current_yaw, +40.0f)

/**
 * @brief D到A段智能转向角度计算
 * @param current_yaw 当前偏航角
 * @return 优化后的右转目标角度
 */
#define SMART_TURN_D_TO_A(current_yaw) \
    SMART_ANGLE_CALC(current_yaw, 40.0f)

/**
 * @brief C到B段智能转向角度计算
 * @param current_yaw 当前偏航角
 * @return 优化后的左转目标角度
 */
#define SMART_TURN_C_TO_B(current_yaw) \
    SMART_ANGLE_CALC(current_yaw, -40.0f)

// =============================================================================
// 调试和监控配置
// =============================================================================

/** @brief 启用角度调试信息 - 设置为1启用OLED调试显示 */
#define ENABLE_ANGLE_DEBUG          1

/** @brief 角度日志输出间隔 - 每隔多少个周期输出一次角度信息 */
#define ANGLE_LOG_INTERVAL         10

/**
 * @brief 角度调试信息输出宏
 * @param line OLED显示行号
 * @param format 格式化字符串
 * @param ... 参数列表
 */
#if ENABLE_ANGLE_DEBUG
#define ANGLE_DEBUG_PRINT(line, format, ...) \
    OLED_Printf(0, 16*(line), 8, format, ##__VA_ARGS__)
#else
#define ANGLE_DEBUG_PRINT(line, format, ...)
#endif

/**
 * @brief 安全角度设置宏 - 带验证的角度设置
 * @param angle 要设置的角度值
 * @description 设置角度前进行有效性检查，无效时使用安全默认值
 */
#define SAFE_ANGLE_SET(angle) \
    do { \
        if (!ANGLE_VERIFY(angle)) { \
            ANGLE_DEBUG_PRINT(3, "ERR:%.1f", angle); \
            angle = 0.0f; \
        } \
    } while(0)

// =============================================================================
// 使用示例和说明
// =============================================================================

/**
 * @brief 配置文件使用示例
 *
 * 在Task_AutoRecover函数中的使用方法：
 *
 * ```c
 * #include "AngleConfig.h"
 *
 * void Task_AutoRecover(void *para) {
 *     // ... 现有代码 ...
 *
 *     switch (current_path) {
 *         case 2:  // B到D段失线
 *             current_path = 3;
 *             // 使用智能角度计算，自动解决180°临界问题
 *             target_angle = SMART_TURN_B_TO_D(wit_data.yaw);
 *             break;
 *
 *         case 3:  // D到A段转向
 *             target_angle = SMART_TURN_D_TO_A(wit_data.yaw);
 *             break;
 *     }
 *
 *     // 安全检查（可选，智能宏已处理）
 *     SAFE_ANGLE_SET(target_angle);
 *
 *     // 设置角度
 *     WIT_SetTargetAngle(target_angle);
 *
 *     // 调试信息输出
 *     ANGLE_DEBUG_PRINT(0, "yaw:%.1f", wit_data.yaw);
 *     ANGLE_DEBUG_PRINT(1, "tgt:%.1f", target_angle);
 *     ANGLE_DEBUG_PRINT(2, "dif:%.1f", ANGLE_DIFF(target_angle, wit_data.yaw));
 * }
 * ```
 *
 * @benefits
 * 1. 自动解决180°临界问题
 * 2. 确保PID控制器接收正确的角度输入
 * 3. 无需修改核心代码，通过配置优化
 * 4. 提供完整的调试和监控支持
 * 5. 支持多种路径的智能角度控制
 */

// =============================================================================
// 第三阶段角度执行偏差修复模块 - 智能角度拦截系统
// =============================================================================

/**
 * @brief 第三阶段目标角度配置
 * @note 用户期望在第三阶段执行160°角度，解决60°执行偏差问题
 */
#define STAGE3_TARGET_ANGLE    160.0f

/**
 * @brief 阶段角度调试开关
 * @note 设置为1启用阶段角度拦截调试信息
 */
#define ENABLE_STAGE_ANGLE_DEBUG    1

/**
 * @brief 外部变量声明 - 角度控制状态管理
 * @note 需要访问这些变量来实现WIT_SetTargetAngle_Original
 */
extern bool Data_wit_ControlEnabled;
extern float Data_wit_UserTarget;

/**
 * @brief 智能角度拦截宏 - 解决第三阶段角度执行偏差的核心技术
 * @param angle 原始角度参数
 * @description
 * 通过宏替换技术，智能拦截WIT_SetTargetAngle函数调用
 * 当current_path == 3时，自动使用STAGE3_TARGET_ANGLE (160°)
 * 其他情况使用原始angle参数，保持完全兼容
 *
 * @implementation
 * 1. 保存原始函数调用
 * 2. 根据current_path进行条件判断
 * 3. 智能选择角度值：第三阶段使用160°，其他阶段使用原值
 * 4. 提供调试信息输出
 * 5. 完全不需要修改用户代码
 */
#ifdef WIT_SetTargetAngle
#undef WIT_SetTargetAngle
#endif

// #define WIT_SetTargetAngle(angle) \
//     do { \
//         float selected_angle = (current_path == 3) ? STAGE3_TARGET_ANGLE : (angle); \
//         Data_wit_UserTarget = selected_angle; \
//         Data_wit_ControlEnabled = true; \
//         STAGE_ANGLE_DEBUG(angle, selected_angle, current_path); \
//     } while(0)

/**
 * @brief 阶段角度拦截调试宏
 * @param original 原始角度值
 * @param selected 选择的角度值
 * @param path 当前路径阶段
 */
#if ENABLE_STAGE_ANGLE_DEBUG
#define STAGE_ANGLE_DEBUG(original, selected, path) \
    do { \
        if (fabs((original) - (selected)) > 1.0f) { \
            ANGLE_DEBUG_PRINT(4, "Stage%d:%.1f->%.1f", path, original, selected); \
        } \
        if ((path) == 3) { \
            ANGLE_DEBUG_PRINT(5, "S3_TARGET:%.1f", selected); \
        } \
    } while(0)
#else
#define STAGE_ANGLE_DEBUG(original, selected, path)
#endif

/**
 * @brief 阶段角度验证宏
 * @param path 当前路径阶段
 * @return 当前阶段应该使用的角度值
 */
#define GET_EXPECTED_STAGE_ANGLE(path) \
    ((path) == 3 ? STAGE3_TARGET_ANGLE : 0.0f)

/**
 * @brief 角度拦截状态检查宏
 * @param path 当前路径阶段
 * @return true如果当前阶段需要角度拦截
 */
#define IS_ANGLE_INTERCEPT_STAGE(path) \
    ((path) == 3)

#endif /* __ANGLE_CONFIG_H */
